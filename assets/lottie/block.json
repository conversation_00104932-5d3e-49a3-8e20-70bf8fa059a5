{"v": "5.7.11", "fr": 25, "ip": 0, "op": 81, "w": 350, "h": 350, "nm": "Comp 13", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Layer 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.507], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.605], "y": [0]}, "t": 20, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 30, "s": [5]}, {"t": 43, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [92.127, 232.212, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-82.873, 57.212, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.652, -2.563], [2.121, -0.265], [0.751, 2.739], [0.751, 4.551], [-0.133, 8.263], [0, 0], [-1.148, 0.177], [0.088, -0.751], [-1.193, -6.584], [-1.811, -4.33]], "o": [[-1.635, 1.237], [-1.105, -2.607], [-1.149, -4.419], [-1.326, -8.086], [0.088, 0.088], [1.061, -0.265], [-0.176, 0.707], [-0.752, 6.584], [0.84, 4.595], [1.458, 3.491]], "v": [[-64.814, 77.07], [-71.752, 78.529], [-74.227, 70.487], [-77.187, 57.01], [-79.042, 32.487], [-78.91, 32.708], [-75.597, 32.001], [-75.949, 34.255], [-74.492, 54.094], [-70.913, 67.703]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.839, -0.618], [2.121, -0.265], [0.751, 2.739], [0.751, 4.551], [-0.133, 8.263], [0, 0], [-1.148, 0.177], [-0.839, 0], [-0.264, -0.132], [-0.044, -0.088], [0, -0.53], [-1.37, -7.114], [-0.397, -1.723], [-1.016, -4.595]], "o": [[-1.635, 1.237], [-1.105, -2.607], [-1.149, -4.419], [-1.326, -8.086], [0.088, 0.088], [1.061, -0.265], [0.84, -0.177], [0.31, 0], [0.044, 0], [0.487, 0.354], [0.795, 7.158], [0.31, 1.767], [1.149, 4.64], [0.353, 1.502]], "v": [[-64.814, 77.07], [-71.752, 78.529], [-74.227, 70.487], [-77.187, 57.01], [-79.042, 32.487], [-78.91, 32.708], [-75.597, 32.001], [-73.033, 31.736], [-72.194, 31.869], [-72.017, 31.957], [-71.398, 33.503], [-68.703, 54.934], [-67.643, 60.236], [-63.887, 74.022]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.96862745285, 0.184313729405, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.784, -0.132], [1.635, -2.121], [0.707, -0.751], [3.094, -1.811], [2.209, -0.309], [1.149, 3.005], [0.088, 1.237], [-1.9, 0.398], [-2.032, 3.446], [-0.574, 2.121], [0.045, 6.937], [-0.707, 5.788], [-1.458, 2.298], [-1.237, 0], [-0.264, -0.132], [-0.044, -0.088], [0, -0.53], [-1.37, -7.114], [-0.397, -1.723], [-1.016, -4.595]], "o": [[-0.133, 2.342], [-0.707, 0.884], [-2.342, 2.739], [-1.944, 1.105], [-2.872, 0.486], [-0.487, -1.282], [1.944, 0.177], [3.889, -0.84], [1.105, -1.856], [1.768, -6.672], [-0.043, -5.833], [0.31, -2.651], [1.28, -0.265], [0.31, 0], [0.044, 0], [0.487, 0.354], [0.795, 7.158], [0.31, 1.767], [1.149, 4.64], [1.016, 4.374]], "v": [[-71.929, 78.529], [-75.243, 85.554], [-77.319, 87.984], [-85.538, 94.833], [-91.724, 97.219], [-99.147, 94.435], [-99.942, 90.68], [-94.11, 90.414], [-84.565, 83.875], [-82.313, 77.777], [-78.999, 57.364], [-79.529, 39.91], [-76.877, 32.222], [-73.033, 31.736], [-72.194, 31.869], [-72.017, 31.957], [-71.398, 33.503], [-68.703, 54.934], [-67.643, 60.236], [-63.887, 74.022]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.784, -0.132], [1.635, -2.121], [0.707, -0.751], [3.094, -1.811], [2.209, -0.309], [1.149, 3.005], [0.088, 1.237], [0, 1.635], [0, 0], [-0.354, -0.265], [-2.165, 0.84], [-1.06, 1.944], [0.708, 11.974], [-0.751, 4.551], [-0.752, -1.281], [0, 0], [-0.708, 0.132], [-1.237, 0], [-0.264, -0.132], [-0.044, -0.088], [0, -0.53], [-1.37, -7.114], [-0.397, -1.723], [-1.016, -4.595]], "o": [[-0.133, 2.342], [-0.707, 0.884], [-2.342, 2.739], [-1.944, 1.105], [-2.872, 0.486], [-0.487, -1.282], [-0.088, -1.546], [0, 0], [0.309, 0.353], [1.855, 1.281], [2.077, -0.84], [5.655, -10.604], [-0.354, -4.639], [1.149, 0.972], [0.088, 0.088], [0.663, -0.177], [1.28, -0.265], [0.31, 0], [0.044, 0], [0.487, 0.354], [0.795, 7.158], [0.31, 1.767], [1.149, 4.64], [1.016, 4.374]], "v": [[-71.929, 78.529], [-75.243, 85.554], [-77.319, 87.984], [-85.538, 94.833], [-91.724, 97.219], [-99.147, 94.435], [-99.942, 90.68], [-99.942, 85.952], [-99.942, 80.87], [-98.926, 81.666], [-92.43, 82.77], [-87.968, 77.998], [-81.782, 42.959], [-81.915, 29.085], [-79.042, 32.487], [-78.91, 32.708], [-76.877, 32.222], [-73.033, 31.736], [-72.194, 31.869], [-72.017, 31.957], [-71.398, 33.503], [-68.703, 54.934], [-67.643, 60.236], [-63.887, 74.022]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.623529434204, 0.109803922474, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.814, -0.134], [1.61, -2.145], [0.669, -0.737], [3.083, -1.809], [2.212, -0.335], [1.139, 3.016], [0, 2.949], [0, 0], [-1.34, 1.408], [-1.072, 0.134], [-1.006, -0.268], [-3.016, -2.815], [-0.738, -1.273], [0, 0], [-1.944, 0], [-0.268, -0.134], [-0.067, -0.067], [0, -0.536], [-1.342, -7.104], [-0.403, -1.743], [-1.006, -4.624]], "o": [[-0.134, 2.346], [-0.736, 0.871], [-2.346, 2.748], [-1.944, 1.14], [-2.881, 0.469], [-1.139, -2.882], [0, 0], [-0.067, -1.943], [0.738, -0.737], [1.005, -0.134], [3.686, 0.871], [1.14, 1.005], [0.067, 0.067], [1.877, -0.469], [0.268, 0], [0.067, 0], [0.468, 0.335], [0.805, 7.171], [0.334, 1.742], [1.139, 4.624], [1.005, 4.357]], "v": [[-71.939, 78.533], [-75.224, 85.571], [-77.301, 87.984], [-85.544, 94.819], [-91.711, 97.232], [-99.149, 94.417], [-99.954, 85.973], [-99.954, 30.01], [-98.547, 24.448], [-95.731, 23.108], [-92.649, 23.375], [-81.926, 29.072], [-79.043, 32.49], [-78.909, 32.691], [-73.012, 31.753], [-72.207, 31.887], [-72.006, 31.954], [-71.403, 33.496], [-68.722, 54.942], [-67.65, 60.237], [-63.896, 74.043]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.792156875134, 0.156862750649, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [7.604, -0.041]], "o": [[-6.557, 3.852], [0, 0]], "v": [[11.921, 45.464], [-9.198, 53.324]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.898039221764, 0.415686279535, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.602, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [10.003, 0.594]], "o": [[-9.003, 4.401], [0, 0]], "v": [[13.827, 32.124], [-14.697, 40.441]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.898039221764, 0.415686279535, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.602, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.158, -1.307], [1.726, -0.419], [3.64, 0.179]], "o": [[-3.08, 1.483], [-1.641, 0.679], [-3.541, 0.859], [0, 0]], "v": [[12.991, 20.739], [3.632, 24.926], [-1.394, 26.681], [-12.274, 27.188]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.898039221764, 0.415686279535, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.602, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-11.356, -9.5], [1.591, -0.398], [4.109, -1.149], [2.43, -1.105], [1.502, 1.281], [0.133, 0.442], [0.177, 1.06], [0.264, 1.37], [0.398, 2.784], [-0.708, 5.7], [-1.723, 1.37], [-1.282, 0.354], [-2.254, 1.061]], "o": [[-1.635, 0.133], [-4.153, 1.016], [-2.563, 0.707], [-1.59, 0.751], [-0.354, -0.309], [-0.353, -1.016], [-0.221, -1.37], [-0.619, -2.784], [-0.883, -5.7], [0.264, -2.165], [1.06, -0.795], [2.298, -0.619], [2.783, 14.581]], "v": [[-46.168, 69.647], [-51.029, 70.399], [-63.4, 73.624], [-70.868, 76.054], [-77.01, 75.922], [-77.673, 74.684], [-78.291, 71.591], [-79.175, 67.438], [-80.766, 59.131], [-81.251, 42.031], [-78.778, 36.11], [-75.066, 34.652], [-68.173, 32.089]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.531, -2.43], [1.724, -2.563], [-0.044, -2.563], [1.326, -1.9], [2.783, -1.635], [13.211, 0.663], [5.346, -0.53], [1.591, -0.398], [4.109, -1.149], [2.43, -1.105], [1.502, 1.281], [0.133, 0.442], [0.177, 1.06], [0.264, 1.37], [0.398, 2.784], [-0.708, 5.7], [-1.723, 1.37], [-1.282, 0.354], [-2.254, 1.061], [-4.109, 3.977], [-5.921, 5.125], [-4.11, 2.165], [-7.865, 3.27], [-1.855, 0.663], [0.441, -3.491], [2.783, -1.856], [4.153, -1.326], [2.784, -3.358], [-0.353, -0.663], [-0.928, 0.265], [-7.821, 2.209], [-1.503, -3.225], [2.563, -2.121], [-0.353, -2.254], [1.811, -1.414]], "o": [[-0.662, 3.137], [2.121, 0.574], [0.044, 2.342], [-1.9, 2.607], [-11.312, 6.805], [-5.346, -0.309], [-1.635, 0.133], [-4.153, 1.016], [-2.563, 0.707], [-1.59, 0.751], [-0.354, -0.309], [-0.353, -1.016], [-0.221, -1.37], [-0.619, -2.784], [-0.883, -5.7], [0.264, -2.165], [1.06, -0.795], [2.298, -0.619], [5.479, -2.43], [5.656, -5.479], [3.491, -3.093], [7.467, -4.021], [1.812, -0.751], [3.093, -1.149], [-0.487, 3.402], [-3.624, 2.43], [-4.153, 1.326], [-0.487, 0.574], [0.398, 0.84], [7.821, -2.165], [2.872, -0.795], [1.326, 2.872], [2.165, 0.795], [0.354, 2.297], [2.474, 0.133]], "v": [[18.386, 37.082], [13.04, 44.417], [15.426, 47.864], [13.35, 54.403], [5.927, 60.545], [-29.997, 69.956], [-46.168, 69.647], [-51.029, 70.399], [-63.4, 73.624], [-70.868, 76.054], [-77.01, 75.922], [-77.673, 74.684], [-78.291, 71.591], [-79.175, 67.438], [-80.766, 59.131], [-81.251, 42.031], [-78.778, 36.11], [-75.066, 34.652], [-68.173, 32.089], [-53.415, 22.236], [-36.359, 5.799], [-24.826, -2.066], [-0.26, -8.429], [5.396, -10.019], [10.434, -6.794], [4.557, 1.513], [-7.506, 6.373], [-18.685, 12.825], [-19.26, 14.813], [-16.741, 15.255], [6.722, 8.671], [14.72, 10.615], [13.217, 20.292], [17.414, 25.373], [14.941, 31.515]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972549021244, 0.568627476692, 0.509803950787, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.425, -0.22], [0.837, -0.722], [1.474, 1.284], [3.925, 3.538], [0.588, 0.642], [0.702, 3.624], [-0.65, 2.131], [-1.569, 1.28], [0.041, -0.885], [-2.655, -4.816], [-1.971, -1.896], [-3.526, -1.111]], "o": [[-0.792, 0.737], [-1.479, 1.31], [-3.85, -3.463], [-0.616, -0.552], [-2.542, -2.584], [-0.428, -2.187], [0.593, -1.951], [-0.267, 0.81], [-0.22, 5.516], [1.314, 2.379], [2.707, 2.538], [1.351, 0.445]], "v": [[69.705, 56.713], [67.285, 58.91], [62.098, 58.899], [50.458, 48.48], [48.623, 46.779], [43.612, 37.145], [43.781, 30.564], [47.106, 25.62], [46.711, 28.185], [50.966, 43.907], [55.802, 50.439], [65.549, 55.694]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.095, -0.003], [0.059, 0.035], [0.487, 1.674], [-0.163, 0.048], [-0.048, -0.166], [-1.337, -0.871], [0.091, -0.143]], "o": [[-0.064, 0.002], [-1.451, -0.952], [-0.048, -0.163], [0.19, -0.043], [0.446, 1.532], [0.141, 0.094], [-0.056, 0.089]], "v": [[67.571, 33.191], [67.389, 33.141], [64.333, 29.011], [64.543, 28.628], [64.932, 28.838], [67.734, 32.621], [67.822, 33.05]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.031, 0.68], [3.258, -2.94], [1.457, 1.303], [3.944, 3.508], [0.603, 0.627], [0.706, 3.587], [-0.625, 2.133], [-4.441, 0.307], [-1.381, -0.45], [0.326, -0.151], [0.802, -1.155], [0.299, -2.133], [-0.354, -1.931], [-4.644, -2.303], [-3.612, 0.682]], "o": [[-3.283, 2.939], [-1.479, 1.306], [-3.893, -3.458], [-0.628, -0.551], [-2.537, -2.606], [-0.43, -2.182], [1.249, -4.267], [1.455, -0.102], [-0.352, 0.076], [-1.254, 0.604], [-1.227, 1.758], [-0.249, 1.932], [0.959, 5.092], [3.289, 1.652], [2.082, -0.404]], "v": [[77.014, 50.127], [67.265, 58.92], [62.121, 58.901], [50.466, 48.502], [48.608, 46.773], [43.628, 37.17], [43.77, 30.571], [53.597, 22.831], [57.888, 23.378], [56.885, 23.73], [53.827, 26.544], [51.526, 32.468], [51.784, 38.289], [60.079, 50.799], [70.869, 51.84]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.815686285496, 0.86274510622, 0.933333337307, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.553, -2.603], [0.627, -0.542], [0.627, -0.542], [2.464, -2.183], [0.837, -0.722], [1.474, 1.284], [3.925, 3.538], [0.588, 0.642], [0.702, 3.624], [-0.65, 2.131], [-1.569, 1.28], [-2.412, 0.204], [-1.351, -0.445], [-0.045, -0.015], [-1.448, -2.573], [0, 0], [-4.408, -0.254], [-1.245, -4.252], [-0.019, -1.004], [0.235, -1.17]], "o": [[-0.567, 0.661], [-0.643, 0.587], [-2.465, 2.182], [-0.792, 0.737], [-1.479, 1.31], [-3.85, -3.463], [-0.616, -0.552], [-2.542, -2.584], [-0.428, -2.187], [0.593, -1.951], [1.778, -1.61], [1.424, -0.08], [0, 0], [2.791, 0.919], [0.015, -0.045], [2.22, -3.86], [4.437, 0.313], [0.319, 1.003], [0.005, 1.199], [-0.676, 3.57]], "v": [[80.729, 46.719], [78.862, 48.45], [77.01, 50.135], [69.705, 56.713], [67.285, 58.91], [62.098, 58.899], [50.458, 48.48], [48.623, 46.779], [43.612, 37.145], [43.781, 30.564], [47.106, 25.62], [53.616, 22.824], [57.874, 23.377], [57.919, 23.392], [64.624, 28.793], [64.684, 28.762], [75.679, 22.801], [85.51, 30.529], [86.001, 33.584], [85.684, 37.123]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.949019610882, 0.972549021244, 0.992156863213, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [64.646, 41.336], "ix": 2}, "a": {"a": 0, "k": [64.646, 41.336], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 12, "s": [100, 100]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 19, "s": [90, 90]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 26, "s": [110, 110]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 33, "s": [90, 90]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 40, "s": [110, 110]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 48, "s": [90, 90]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.167, 0.167], "y": [0, 0]}, "t": 55, "s": [110, 110]}, {"t": 62, "s": [100, 100]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.728, 2.872], [0, 0], [11.533, 2.298], [5.082, 5.7], [1.37, 3.712], [-3.137, 6.805], [-4.86, 2.916], [-11.532, 3.667]], "o": [[0, 0], [-11.444, 2.739], [-7.467, -1.458], [-2.65, -2.916], [-2.563, -7.025], [2.43, -5.125], [10.428, -6.186], [5.259, -1.679]], "v": [[99.998, 0.005], [99.998, 71.983], [65.002, 72.69], [44.809, 62.792], [38.977, 52.585], [38.712, 30.714], [50.509, 18.739], [84.709, 6.721]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.035294119269, 0.364705890417, 0.643137276173, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.744, -1.193], [13.035, 0], [5.744, 1.149], [-13.035, 0]], "o": [[-5.744, 1.149], [-13.035, 0], [5.744, -1.193], [13.035, 0]], "v": [[93.282, -4.635], [63.147, -2.691], [33.012, -4.635], [63.147, -6.667]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.658823549747, 0.337254911661, 0.384313732386, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -2.077], [2.562, -0.53], [13.035, 0], [5.744, 1.149], [0, 0.663], [-18.867, 0]], "o": [[0, 0.663], [-5.744, 1.149], [-13.035, 0], [-2.563, -0.53], [0, -2.077], [18.867, 0]], "v": [[97.302, -6.446], [93.282, -4.635], [63.147, -2.691], [33.012, -4.635], [28.991, -6.446], [63.147, -10.202]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.949019610882, 0.972549021244, 0.992156863213, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -2.243], [20.363, 0], [0, 2.243], [-20.363, 0]], "o": [[0, 2.243], [-20.363, 0], [0, -2.243], [20.363, 0]], "v": [[100, -6.449], [63.13, -2.388], [26.26, -6.449], [63.13, -10.511]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.035294119269, 0.364705890417, 0.643137276173, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [7.291, -0.884], [9.19, -1.502], [6.274, -6.937], [0.044, -0.044], [0, 0]], "o": [[0, 0], [-6.275, 3.667], [-9.278, 1.105], [-9.235, 1.546], [-0.044, 0.044], [0, 0], [0, 0]], "v": [[99.998, -6.459], [99.998, -3.897], [79.053, 2.378], [51.173, 4.808], [26.384, 16.694], [26.296, 16.782], [26.296, -6.459]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [20.37, 0], [0, 2.254], [0, 0]], "o": [[0, 0], [0, 2.254], [-20.37, 0], [0, 0], [0, 0]], "v": [[99.998, -6.459], [99.998, 77.184], [63.147, 81.249], [26.296, 77.184], [26.296, -6.459]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.203921571374, 0.364705890417, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.574, -3.888], [-1.017, -4.374], [-1.016, -2.165], [-1.149, -1.193], [-2.828, -0.884], [-2.297, 0.574], [-1.413, 2.121], [-1.9, 1.723], [-0.618, 0], [-0.398, -0.663], [0.044, -1.944], [0.663, -2.121], [0.707, -2.651], [0.398, -0.354], [0.795, 0.574], [0.353, 0.574], [0.044, 0.044], [2.695, 0.663], [1.325, -0.177], [3.491, 7.512], [0.265, 3.8], [-0.663, 4.463], [-2.342, 3.977], [-3.756, 1.502], [-2.077, -0.619], [-1.547, -0.265], [-0.574, 0.221], [-0.177, 0.707], [-0.574, 0.398], [-0.087, 0.707], [-0.354, 0.574], [-1.193, -0.574], [-0.618, -1.149], [0.31, -8.086], [0, 0], [1.68, 2.474], [2.828, 0.928], [2.429, -2.033], [1.149, -2.96]], "o": [[-0.619, 4.463], [0.574, 2.342], [0.708, 1.547], [2.032, 2.209], [2.254, 0.707], [2.475, -0.663], [1.414, -2.165], [0.442, -0.398], [0.795, 0], [0.972, 1.679], [-0.044, 2.253], [-0.795, 2.563], [-0.133, 0.486], [-0.662, 0.663], [-0.575, -0.398], [0, -0.044], [-0.487, -2.739], [-1.282, -0.309], [-8.263, 1.016], [-1.591, -3.491], [-0.265, -4.507], [0.662, -4.551], [2.077, -3.491], [2.032, -0.796], [1.503, 0.442], [0.619, 0.088], [0.619, -0.265], [0.398, 0.353], [0.531, -0.398], [0.088, -0.663], [0.707, -1.149], [1.193, 0.574], [3.889, 7.07], [0, 0], [-2.43, -1.679], [-1.634, -2.474], [-3.049, -1.016], [-2.475, 2.032], [-1.413, 3.712]], "v": [[-1.85, 30.139], [-1.762, 43.572], [0.756, 50.332], [3.408, 54.618], [11.273, 58.727], [18.209, 59.567], [23.954, 54.707], [28.461, 48.476], [30.008, 47.681], [31.863, 49.006], [32.835, 54.707], [31.819, 60.937], [30.317, 68.978], [29.654, 70.348], [27.003, 70.304], [25.677, 68.758], [25.633, 68.669], [21.038, 62.218], [17.061, 62.351], [-3.309, 51.039], [-5.739, 39.904], [-5.12, 26.339], [-0.922, 13.26], [8.931, 5.837], [15.161, 6.102], [19.756, 7.163], [21.612, 7.119], [22.805, 5.572], [24.219, 5.705], [25.058, 3.893], [25.545, 1.949], [29.124, 1.198], [31.73, 4.07], [35.133, 27.709], [27.18, 21.435], [21.523, 14.586], [15.072, 8.709], [6.192, 10.786], [0.977, 18.651]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.574, -3.888], [-1.017, -4.374], [-1.016, -2.165], [-1.149, -1.193], [-2.828, -0.884], [-2.297, 0.574], [-1.413, 2.121], [-1.9, 1.723], [-0.618, 0], [-0.398, -0.663], [0.044, -1.944], [0.663, -2.121], [0.707, -2.651], [0.398, -0.354], [0.795, 0.574], [0.353, 0.574], [0.044, 0.044], [0.133, 0.177], [2.165, -0.619], [2.386, 0], [1.634, 0.53], [1.635, 0.972], [1.503, 2.872], [-1.503, 9.544], [-6.319, 4.021], [-3.932, 0.751], [-3.578, -1.458], [-0.309, -0.133], [-0.574, 0.398], [-0.087, 0.707], [-0.354, 0.574], [-1.193, -0.574], [-0.618, -1.149], [0.31, -8.086], [0, 0], [1.68, 2.474], [2.828, 0.928], [2.429, -2.033], [1.149, -2.96]], "o": [[-0.619, 4.463], [0.574, 2.342], [0.708, 1.547], [2.032, 2.209], [2.254, 0.707], [2.475, -0.663], [1.414, -2.165], [0.442, -0.398], [0.795, 0], [0.972, 1.679], [-0.044, 2.253], [-0.795, 2.563], [-0.133, 0.486], [-0.662, 0.663], [-0.575, -0.398], [0, -0.044], [-0.133, -0.221], [-1.37, -2.342], [-2.298, 0.707], [-1.767, 0], [-1.768, -0.619], [-2.783, -1.635], [-4.507, -8.616], [1.105, -7.114], [3.269, -2.077], [3.8, -0.751], [0.31, 0.133], [0.398, 0.353], [0.531, -0.398], [0.088, -0.663], [0.707, -1.149], [1.193, 0.574], [3.889, 7.07], [0, 0], [-2.43, -1.679], [-1.634, -2.474], [-3.049, -1.016], [-2.475, 2.032], [-1.413, 3.712]], "v": [[-1.85, 30.139], [-1.762, 43.572], [0.756, 50.332], [3.408, 54.618], [11.273, 58.727], [18.209, 59.567], [23.954, 54.707], [28.461, 48.476], [30.008, 47.681], [31.863, 49.006], [32.835, 54.707], [31.819, 60.937], [30.317, 68.978], [29.654, 70.348], [27.003, 70.304], [25.677, 68.758], [25.633, 68.669], [25.279, 68.05], [18.078, 66.858], [11.184, 68.625], [6.104, 67.52], [0.889, 65.311], [-5.606, 58.241], [-10.025, 29.432], [0.669, 9.019], [10.565, 4.114], [21.921, 5.086], [22.805, 5.572], [24.219, 5.705], [25.058, 3.893], [25.545, 1.949], [29.124, 1.198], [31.73, 4.07], [35.133, 27.709], [27.18, 21.435], [21.523, 14.586], [15.072, 8.709], [6.192, 10.786], [0.977, 18.651]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.96862745285, 0.184313729405, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-5.361, 33.686], "ix": 2}, "a": {"a": 0, "k": [-5.361, 33.686], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [0]}, {"i": {"x": [0.507], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.605], "y": [0]}, "t": 22, "s": [-5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [3]}, {"t": 45, "s": [0]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 9, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-74.535, 55.561], "ix": 2}, "a": {"a": 0, "k": [-74.535, 55.561], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1, "s": [0]}, {"i": {"x": [0.507], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.605], "y": [0]}, "t": 21, "s": [-5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 31, "s": [3]}, {"t": 44, "s": [0]}], "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 120, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 70, "s": [100]}, {"t": 73, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [237.907, 170.752, 0], "to": [0, -12.5, 0], "ti": [0, 12.5, 0]}, {"t": 78, "s": [237.907, 95.752, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [62.907, -4.248, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 48, "s": [0, 0, 100]}, {"t": 59, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar fx = effect('Kleaner');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON><PERSON><PERSON>", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.058, 4.322], [1.251, 3.072], [4.224, 1.539], [5.304, 5.61], [0.244, 4.226], [-0.696, 2.151], [-3.437, 2.744], [-4.411, -5.053], [-1.389, -2.912], [-1.16, 2.587], [-4.384, -8.118], [0.346, -3.976], [2.527, -4.345], [1.771, -1.846], [1.75, -1.788], [-0.104, -0.401], [-1.272, 0.59], [-2.536, 4.443], [-1.342, 4.45], [1.18, 4.495], [7.635, 1.033], [3.509, -7.457], [1.831, 0.987], [4.729, -1.01], [-0.781, -12.189], [-0.837, -1.914], [-3.2, -1.819], [-0.328, -2.656], [-0.272, -3.722], [-2.1, -0.565], [-2.118, 0.493], [-2.174, -0.05], [-0.408, -1.6], [-1.489, -1.545], [-0.591, -0.33], [0.009, -2.541], [-0.834, -2.39], [-2.472, -0.545], [-0.37, 0.581], [0.039, 0.35], [0.719, 1.402]], "o": [[1.426, -2.995], [-1.695, -4.164], [-7.254, -2.643], [-2.908, -3.076], [-0.13, -2.257], [1.353, -4.184], [5.215, -4.164], [2.122, 2.43], [0.183, -2.83], [3.983, -8.881], [1.896, 3.511], [-0.437, 5.02], [-1.285, 2.21], [-1.808, 1.883], [-0.29, 0.296], [0.687, 2.645], [4.695, -2.177], [2.304, -4.037], [1.342, -4.45], [-1.957, -7.452], [-8.329, -1.127], [-0.864, -1.893], [-4.257, -2.294], [-12.377, 2.644], [0.134, 2.085], [1.475, 3.373], [2.327, 1.322], [0.458, 3.704], [0.159, 2.169], [2.1, 0.565], [2.118, -0.493], [1.651, 0.038], [0.531, 2.079], [0.469, 0.487], [2.693, 1.504], [-0.009, 2.532], [0.834, 2.39], [0.673, 0.148], [0.189, -0.297], [-0.174, -1.566], [-2.498, -4.872]], "v": [[64.793, -26.11], [65.868, -35.711], [55.125, -43.15], [35.423, -54.922], [29.915, -66.074], [30.958, -72.736], [38.015, -83.66], [53.504, -82.276], [58.43, -73.981], [59.972, -82.298], [80.25, -82.537], [82.217, -70.9], [77.551, -56.602], [72.974, -50.463], [66.981, -45.741], [66.52, -44.647], [75.832, -46.239], [86.065, -56.41], [91.901, -69.04], [92.522, -82.794], [76.197, -97.236], [57.052, -86.642], [52.682, -90.943], [38.55, -92.396], [20.902, -67.797], [22.136, -61.685], [29.902, -54.107], [35.315, -48.609], [31.344, -38.402], [35.53, -33.946], [41.978, -34.238], [48.393, -35.305], [52.366, -32.974], [52.111, -26.801], [53.876, -25.822], [55.808, -18.224], [56.634, -10.713], [61.783, -5.582], [63.661, -6.058], [63.83, -7.08], [61.783, -11.298]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.815686285496, 0.86274510622, 0.933333337307, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 48, "op": 78, "st": 48, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 42, "s": [100]}, {"t": 45, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 21, "s": [237.907, 170.752, 0], "to": [0, -12.5, 0], "ti": [0, 12.5, 0]}, {"t": 50, "s": [237.907, 95.752, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [62.907, -4.248, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 20, "s": [0, 0, 100]}, {"t": 31, "s": [100, 100, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar fx = effect('Kleaner');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON><PERSON><PERSON>", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.058, 4.322], [1.251, 3.072], [4.224, 1.539], [5.304, 5.61], [0.244, 4.226], [-0.696, 2.151], [-3.437, 2.744], [-4.411, -5.053], [-1.389, -2.912], [-1.16, 2.587], [-4.384, -8.118], [0.346, -3.976], [2.527, -4.345], [1.771, -1.846], [1.75, -1.788], [-0.104, -0.401], [-1.272, 0.59], [-2.536, 4.443], [-1.342, 4.45], [1.18, 4.495], [7.635, 1.033], [3.509, -7.457], [1.831, 0.987], [4.729, -1.01], [-0.781, -12.189], [-0.837, -1.914], [-3.2, -1.819], [-0.328, -2.656], [-0.272, -3.722], [-2.1, -0.565], [-2.118, 0.493], [-2.174, -0.05], [-0.408, -1.6], [-1.489, -1.545], [-0.591, -0.33], [0.009, -2.541], [-0.834, -2.39], [-2.472, -0.545], [-0.37, 0.581], [0.039, 0.35], [0.719, 1.402]], "o": [[1.426, -2.995], [-1.695, -4.164], [-7.254, -2.643], [-2.908, -3.076], [-0.13, -2.257], [1.353, -4.184], [5.215, -4.164], [2.122, 2.43], [0.183, -2.83], [3.983, -8.881], [1.896, 3.511], [-0.437, 5.02], [-1.285, 2.21], [-1.808, 1.883], [-0.29, 0.296], [0.687, 2.645], [4.695, -2.177], [2.304, -4.037], [1.342, -4.45], [-1.957, -7.452], [-8.329, -1.127], [-0.864, -1.893], [-4.257, -2.294], [-12.377, 2.644], [0.134, 2.085], [1.475, 3.373], [2.327, 1.322], [0.458, 3.704], [0.159, 2.169], [2.1, 0.565], [2.118, -0.493], [1.651, 0.038], [0.531, 2.079], [0.469, 0.487], [2.693, 1.504], [-0.009, 2.532], [0.834, 2.39], [0.673, 0.148], [0.189, -0.297], [-0.174, -1.566], [-2.498, -4.872]], "v": [[64.793, -26.11], [65.868, -35.711], [55.125, -43.15], [35.423, -54.922], [29.915, -66.074], [30.958, -72.736], [38.015, -83.66], [53.504, -82.276], [58.43, -73.981], [59.972, -82.298], [80.25, -82.537], [82.217, -70.9], [77.551, -56.602], [72.974, -50.463], [66.981, -45.741], [66.52, -44.647], [75.832, -46.239], [86.065, -56.41], [91.901, -69.04], [92.522, -82.794], [76.197, -97.236], [57.052, -86.642], [52.682, -90.943], [38.55, -92.396], [20.902, -67.797], [22.136, -61.685], [29.902, -54.107], [35.315, -48.609], [31.344, -38.402], [35.53, -33.946], [41.978, -34.238], [48.393, -35.305], [52.366, -32.974], [52.111, -26.801], [53.876, -25.822], [55.808, -18.224], [56.634, -10.713], [61.783, -5.582], [63.661, -6.058], [63.83, -7.08], [61.783, -11.298]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.815686285496, 0.86274510622, 0.933333337307, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 50, "st": 20, "bm": 0}], "markers": []}