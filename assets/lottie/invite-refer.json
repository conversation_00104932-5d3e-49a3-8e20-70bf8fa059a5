{"v": "5.7.11", "fr": 25, "ip": 0, "op": 81, "w": 350, "h": 350, "nm": "Comp 27", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Layer 10", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 51, "s": [100]}, {"t": 52, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [18.28]}, {"t": 54, "s": [49.28]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [26.705, -89.343, 0], "to": [11.528, -5.006, 0], "ti": [-15.48, 8.959, 0]}, {"t": 54, "s": [115.885, -135.798, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [48.37, -7.474, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 39, "s": [0, 0, 100]}, {"t": 42, "s": [90.415, 90.415, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar fx = effect('Kleaner');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON><PERSON><PERSON>", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.894, -1.746], [2.301, -2.198], [0.282, 0.27], [1.378, 2.693], [-0.563, 1.879], [-1.281, 0.465], [-1.138, -0.751], [-0.075, -1.359], [-1.138, 0.751], [-1.281, -0.465], [-0.39, -1.307]], "o": [[-1.378, 2.693], [-0.282, 0.27], [-2.298, -2.2], [-0.894, -1.749], [0.39, -1.307], [1.281, -0.468], [1.138, 0.751], [0.078, -1.362], [1.138, -0.751], [1.281, 0.468], [0.559, 1.883]], "v": [[55.88, -15.064], [48.873, -7.676], [47.867, -7.677], [40.861, -15.064], [39.782, -20.703], [42.487, -23.574], [46.405, -23.115], [48.369, -19.695], [50.336, -23.112], [54.255, -23.574], [56.96, -20.703]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.223529413342, 0.388235300779, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 39, "op": 54, "st": 19, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 9", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 42, "s": [100]}, {"t": 43, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [18.28]}, {"t": 45, "s": [49.28]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [26.705, -89.343, 0], "to": [11.528, -5.006, 0], "ti": [-15.48, 8.959, 0]}, {"t": 45, "s": [115.885, -135.798, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [48.37, -7.474, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 30, "s": [0, 0, 100]}, {"t": 33, "s": [90.415, 90.415, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar fx = effect('Kleaner');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON><PERSON><PERSON>", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.894, -1.746], [2.301, -2.198], [0.282, 0.27], [1.378, 2.693], [-0.563, 1.879], [-1.281, 0.465], [-1.138, -0.751], [-0.075, -1.359], [-1.138, 0.751], [-1.281, -0.465], [-0.39, -1.307]], "o": [[-1.378, 2.693], [-0.282, 0.27], [-2.298, -2.2], [-0.894, -1.749], [0.39, -1.307], [1.281, -0.468], [1.138, 0.751], [0.078, -1.362], [1.138, -0.751], [1.281, 0.468], [0.559, 1.883]], "v": [[55.88, -15.064], [48.873, -7.676], [47.867, -7.677], [40.861, -15.064], [39.782, -20.703], [42.487, -23.574], [46.405, -23.115], [48.369, -19.695], [50.336, -23.112], [54.255, -23.574], [56.96, -20.703]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.223529413342, 0.388235300779, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 30, "op": 45, "st": 10, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 8", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [100]}, {"t": 33, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [18.28]}, {"t": 35, "s": [49.28]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [26.705, -89.343, 0], "to": [11.528, -5.006, 0], "ti": [-15.48, 8.959, 0]}, {"t": 35, "s": [115.885, -135.798, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [48.37, -7.474, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 20, "s": [0, 0, 100]}, {"t": 23, "s": [90.415, 90.415, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar fx = effect('Kleaner');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON><PERSON><PERSON>", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.894, -1.746], [2.301, -2.198], [0.282, 0.27], [1.378, 2.693], [-0.563, 1.879], [-1.281, 0.465], [-1.138, -0.751], [-0.075, -1.359], [-1.138, 0.751], [-1.281, -0.465], [-0.39, -1.307]], "o": [[-1.378, 2.693], [-0.282, 0.27], [-2.298, -2.2], [-0.894, -1.749], [0.39, -1.307], [1.281, -0.468], [1.138, 0.751], [0.078, -1.362], [1.138, -0.751], [1.281, 0.468], [0.559, 1.883]], "v": [[55.88, -15.064], [48.873, -7.676], [47.867, -7.677], [40.861, -15.064], [39.782, -20.703], [42.487, -23.574], [46.405, -23.115], [48.369, -19.695], [50.336, -23.112], [54.255, -23.574], [56.96, -20.703]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.223529413342, 0.388235300779, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 35, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 4", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [175, 175, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [6.736, 0.54]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-7.584, -0.608]], "v": [[-20.909, 44.138], [-100.05, 202.252], [159.979, 201.479], [24.212, 47.591], [1.805, 55.314]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.776470601559, 0.776470601559, 0.75686275959, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.811, 15.348], [0.251, -0.596], [0.703, -0.761], [5.292, 0.291], [0, 0], [3.14, 1.908], [-1.268, -5.424], [-7.452, -0.362]], "o": [[0.16, -3.028], [-0.703, 0.761], [-4.652, 2.539], [0, 0], [-3.527, -0.659], [-2.814, -1.71], [2.559, 10.943], [10.319, 0.501]], "v": [[23.936, 47.152], [23.717, 53.052], [21.607, 55.334], [6.295, 58.794], [-4.63, 57.164], [-14.901, 53.91], [-25.215, 44.995], [-4.217, 57.46]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.776470601559, 0.776470601559, 0.75686275959, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.612, 0.164]], "o": [[4.497, 1.034], [0, 0]], "v": [[-8.298, 61.029], [5.444, 62.342]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.776470601559, 0.776470601559, 0.75686275959, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 120, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Layer 2", "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [-5]}, {"t": 20, "s": [5]}], "ix": 10, "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [184.122, 423.149, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [184.122, 230.149, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [185.442, 239.107, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 70, "s": [184.122, 230.149, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 80, "s": [184.122, 423.149, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9.122, 55.149, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8, "s": [51, 51, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 13, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 70, "s": [100, 100, 100]}, {"t": 75, "s": [51, 51, 100]}], "ix": 6, "l": 2, "x": "var $bm_rt;\nvar fx = effect('Kleaner');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}}, "ao": 0, "ef": [{"ty": 5, "nm": "<PERSON><PERSON><PERSON>", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.039, 1.868], [-0.566, 2.062]], "o": [[2.012, -0.722], [1.039, -1.868], [0, 0]], "v": [[8.403, 48.744], [13.02, 44.494], [15.201, 38.48]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.417, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.721, 0.078]], "o": [[1.197, 1.239], [0, 0]], "v": [[3.015, -33.568], [7.676, -31.714]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.807, -0.417]], "o": [[1.319, 1.303], [0, 0]], "v": [[9.236, -46.764], [14.047, -44.117]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.196, -0.287]], "o": [[0.196, 0.287], [0, 0]], "v": [[-17.793, -21.364], [-17.204, -20.504]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.458, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.296, -3.253]], "o": [[-1.963, 2.9], [0, 0]], "v": [[-18.244, -34.385], [-19.332, -24.349]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.458, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.092, -1.642], [1.787, 0.771], [0.306, 0.745], [-0.133, 0.761], [-0.576, 0.833], [-1.453, -1.858]], "o": [[-1.078, 1.621], [-0.74, -0.319], [-0.294, -0.715], [0.175, -0.997], [1.181, -1.709], [1.199, 1.533]], "v": [[21.325, -54.084], [16.168, -52.554], [14.461, -54.18], [14.355, -56.465], [15.415, -59.286], [20.844, -59.93]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.102, 7.282], [0.96, 1.417], [1.951, -0.443], [1.38, -1.449], [2.224, -4.618], [4.894, -7.038]], "o": [[4.086, -6.128], [0.256, -1.692], [-1.122, -1.657], [-1.951, 0.443], [-3.534, 3.712], [-3.719, 7.723], [0, 0]], "v": [[14.642, -35.861], [24.021, -55.66], [23.365, -60.629], [18.009, -62.427], [13.022, -59.245], [5.193, -46.091], [-7.754, -23.903]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.65098041296, 0.423529416323, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 3, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.175, -6.199], [8.168, -7.804], [1.001, 0.958], [4.894, 9.563], [-1.997, 6.673], [-4.548, 1.651], [-4.041, -2.667], [-0.266, -4.826], [-4.041, 2.667], [-4.548, -1.651], [-1.385, -4.641]], "o": [[-4.894, 9.562], [-1.002, 0.957], [-8.159, -7.812], [-3.175, -6.211], [1.385, -4.641], [4.548, -1.662], [4.041, 2.667], [0.277, -4.837], [4.041, -2.667], [4.548, 1.662], [1.986, 6.684]], "v": [[38.948, -68.87], [14.067, -42.639], [10.496, -42.641], [-14.376, -68.87], [-18.209, -88.887], [-8.604, -99.081], [5.307, -97.454], [12.28, -85.309], [19.264, -97.442], [33.175, -99.081], [42.78, -88.888]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.223529413342, 0.388235300779, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15.668, -57.59], "ix": 2}, "a": {"a": 0, "k": [15.668, -57.59], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 0, "s": [100, 100]}, {"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 10, "s": [120, 120]}, {"t": 20, "s": [100, 100]}], "ix": 3, "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 19", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.379, -1.226]], "o": [[-0.694, 1.709], [0, 0]], "v": [[14.86, -24.995], [11.696, -20.514]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 11", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.256, -2.752]], "o": [[0.316, 3.008], [0, 0]], "v": [[16.766, -2.497], [15.321, 6.354]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 12", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.085, -0.199], [1.903, -1.613]], "o": [[2.83, -1.245], [-0.475, 2.449], [0, 0]], "v": [[-11.723, -29.761], [-2.676, -31.36], [-7.269, -24.958]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 13", "np": 2, "cix": 2, "bm": 0, "ix": 11, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [4.163, -3.474]], "o": [[-4.398, 3.172], [0, 0]], "v": [[9.533, -46.909], [-3.314, -36.935]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 14", "np": 2, "cix": 2, "bm": 0, "ix": 12, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.559, 2.786]], "o": [[-2.152, -1.855], [0, 0]], "v": [[3.6, -42.662], [-0.621, -49.889]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 15", "np": 2, "cix": 2, "bm": 0, "ix": 13, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [5.883, 1.004]], "o": [[-5.326, -2.693], [0, 0]], "v": [[5.891, -33.272], [-11.05, -38.859]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 16", "np": 2, "cix": 2, "bm": 0, "ix": 14, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.554, -0.982], [-0.604, 0], [-0.51, 0.719], [1.159, 0.84], [0.714, 0.659], [0.016, -0.28]], "o": [[0.516, 0.34], [0.868, 0.022], [-0.379, -1.356], [-0.775, -0.565], [-0.791, 0.374], [-0.12, 1.834]], "v": [[27.804, -33.575], [29.495, -32.949], [31.642, -34.197], [28.797, -37.293], [26.556, -39.126], [25.04, -38.071]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.96862745285, 0.552941203117, 0.321568638086, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 17", "np": 2, "cix": 2, "bm": 0, "ix": 15, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.332, -0.195], [-4.797, 2.971], [0.168, 1.798], [0.526, 6.662], [-2.546, 4.822], [1.687, 3.574], [0.346, 1.312], [-0.246, 2.334], [1.155, 4.228], [-1.877, 3.976], [-1.444, -1.037], [-1.56, -0.889], [-1.713, 0.517], [0.434, 1.741], [0.011, 0.033], [1.159, 0.84], [0.714, 0.659], [1.511, 2.498], [-2.97, 2.401], [0.083, 1.071], [0.873, 0.587], [2.438, -0.155], [0.165, 1.483], [1.455, 0.356], [1.362, -0.473], [6.818, -6.613], [5.413, -5.229], [0.505, -1.889], [-1.703, -2.745], [-1.523, -5.551], [0.802, -11.848], [0.118, -5.787], [-3.47, -0.816]], "o": [[5.879, 0.859], [1.535, -0.951], [-0.621, -6.644], [-0.425, -5.442], [1.855, -3.493], [-0.593, -1.23], [-0.583, -2.295], [0.482, -4.371], [-1.16, -4.244], [1.779, 0.01], [1.461, 1.032], [1.543, 0.895], [1.713, -0.517], [-0.011, -0.033], [-0.379, -1.356], [-0.774, -0.565], [-2.247, -2.014], [3.822, -0.012], [0.851, -0.687], [-0.094, -1.049], [-2.01, -1.361], [1.334, -0.665], [-0.171, -1.499], [-1.4, -0.356], [-8.938, 3.166], [-5.413, 5.229], [-1.411, 1.368], [-0.806, 3.125], [3.011, 4.908], [3.146, 11.514], [-0.393, 5.804], [-0.086, 4.209], [1.185, 0.279]], "v": [[-0.273, 54.696], [17.346, 52.424], [19.481, 47.85], [17.715, 27.912], [18.885, 12.163], [19.139, 0.747], [17.408, -2.959], [17.889, -9.965], [15.672, -22.863], [14.635, -36.009], [19.54, -34.094], [23.884, -30.949], [29.024, -30.028], [31.68, -34.081], [31.642, -34.197], [28.797, -37.293], [26.556, -39.126], [20.771, -46.037], [33.471, -49.671], [34.991, -52.412], [33.189, -54.849], [26.138, -56.615], [28.158, -60.328], [25.389, -63.523], [21.172, -62.939], [-5.429, -51.646], [-21.661, -35.942], [-25.042, -31.208], [-22.112, -22.264], [-15.408, -6.396], [-12.716, 29.017], [-14.58, 46.536], [-4.068, 53.976]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.65098041296, 0.423529416323, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 18", "np": 2, "cix": 2, "bm": 0, "ix": 16, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 120, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Layer 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [175, 175, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.573, -2.726], [-2.401, -1.413], [-1.829, 2.101], [-0.725, 0.655], [-1.737, -2.433], [-1.684, -2.47], [-2.344, 1.856], [-0.779, 0.738], [-2.144, -0.902], [-1.648, -1.642]], "o": [[-0.195, 2.779], [0.573, 2.726], [2.401, 1.413], [0.641, -0.737], [2.218, -2.005], [1.737, 2.433], [1.684, 2.47], [0.841, -0.666], [1.689, -1.6], [2.144, 0.902], [0, 0]], "v": [[-92.239, -26.768], [-92.052, -18.441], [-87.628, -11.636], [-79.975, -12.509], [-78.185, -14.821], [-70.508, -12.831], [-66.449, -4.851], [-58.816, -2.518], [-56.689, -4.927], [-50.139, -5.68], [-44.642, -1.456]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44.5, "s": [100]}, {"t": 58.25, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 58.25, "s": [100]}, {"t": 72, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.223529413342, 0.388235300779, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.083, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 44.5, "s": [0, 0], "to": [-6.667, -3.333], "ti": [6.667, 3.333]}, {"t": 72, "s": [-40, -20]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-6.275, -0.896], [-1.83, -6.069], [4.733, -4.216], [2.573, 2.798], [-0.947, 1.98], [-2.055, 0.771], [-4.274, -3.479], [-2.835, -4.727]], "o": [[3.455, -5.314], [6.275, 0.896], [1.83, 6.069], [-2.839, 2.528], [-1.486, -1.615], [0.947, -1.98], [5.16, -1.936], [4.274, 3.479], [0, 0]], "v": [[-77.67, -44.159], [-61.272, -51.605], [-47.613, -39.868], [-52.505, -22.537], [-62.721, -21.491], [-63.417, -27.654], [-58.528, -31.822], [-43.122, -28.358], [-33.092, -15.325]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.92549020052, 0.482352942228, 0.937254905701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.083, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [100]}, {"t": 52.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52.75, "s": [100]}, {"t": 66.5, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [0, 0], "to": [-6.667, -3.333], "ti": [6.667, 3.333]}, {"t": 66.5, "s": [-40, -20]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.209, 1.55], [-3.534, 0.457], [-2.261, 2.755], [-1.457, 2.532], [-3.118, 1.362], [-3.335, 0.674]], "o": [[-0.974, -3.428], [3.209, -1.55], [3.534, -0.457], [1.854, -2.258], [1.697, -2.949], [3.118, -1.362], [0, 0]], "v": [[46.251, 25.333], [51.092, 16.772], [61.519, 14.664], [71.063, 10.453], [74.905, 2.609], [82.615, -3.814], [92.433, -6.532]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52.75, "s": [0]}, {"t": 66.5, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [0]}, {"t": 52.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.854901969433, 0.254901975393, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.083, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [0, 0], "to": [6.667, -3.333], "ti": [-6.667, 3.333]}, {"t": 66.5, "s": [40, -20]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.853, 7.248], [-7.332, 1.489], [-1.192, -7.386], [2.864, -1.24], [2.134, 2.953], [-3.689, 7.649], [-8.001, 2.847]], "o": [[-2.766, -6.952], [1.853, -7.248], [7.332, -1.489], [0.497, 3.081], [-3.343, 1.448], [-4.975, -6.883], [3.689, -7.649], [0, 0]], "v": [[39.367, 7.616], [37.046, -14.334], [51.842, -29.638], [68.839, -18.926], [65.401, -10.947], [56.144, -14.978], [54.614, -39.196], [73.501, -55.324]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.415686279535, 0.847058832645, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.083, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56.875, "s": [0]}, {"t": 70.625, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43.125, "s": [0]}, {"t": 56.875, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 43.125, "s": [0, 0], "to": [6.667, -3.333], "ti": [-6.667, 3.333]}, {"t": 70.625, "s": [40, -20]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 39, "op": 72, "st": 39, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Layer 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [175, 175, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.573, -2.726], [-2.401, -1.413], [-1.829, 2.101], [-0.725, 0.655], [-1.737, -2.433], [-1.684, -2.47], [-2.344, 1.856], [-0.779, 0.738], [-2.144, -0.902], [-1.648, -1.642]], "o": [[-0.195, 2.779], [0.573, 2.726], [2.401, 1.413], [0.641, -0.737], [2.218, -2.005], [1.737, 2.433], [1.684, 2.47], [0.841, -0.666], [1.689, -1.6], [2.144, 0.902], [0, 0]], "v": [[-92.239, -26.768], [-92.052, -18.441], [-87.628, -11.636], [-79.975, -12.509], [-78.185, -14.821], [-70.508, -12.831], [-66.449, -4.851], [-58.816, -2.518], [-56.689, -4.927], [-50.139, -5.68], [-44.642, -1.456]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25.5, "s": [100]}, {"t": 39.25, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39.25, "s": [100]}, {"t": 53, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.223529413342, 0.388235300779, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.083, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25.5, "s": [0, 0], "to": [-6.667, -3.333], "ti": [6.667, 3.333]}, {"t": 53, "s": [-40, -20]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-6.275, -0.896], [-1.83, -6.069], [4.733, -4.216], [2.573, 2.798], [-0.947, 1.98], [-2.055, 0.771], [-4.274, -3.479], [-2.835, -4.727]], "o": [[3.455, -5.314], [6.275, 0.896], [1.83, 6.069], [-2.839, 2.528], [-1.486, -1.615], [0.947, -1.98], [5.16, -1.936], [4.274, 3.479], [0, 0]], "v": [[-77.67, -44.159], [-61.272, -51.605], [-47.613, -39.868], [-52.505, -22.537], [-62.721, -21.491], [-63.417, -27.654], [-58.528, -31.822], [-43.122, -28.358], [-33.092, -15.325]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.92549020052, 0.482352942228, 0.937254905701, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.083, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [100]}, {"t": 33.75, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33.75, "s": [100]}, {"t": 47.5, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [-6.667, -3.333], "ti": [6.667, 3.333]}, {"t": 47.5, "s": [-40, -20]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.209, 1.55], [-3.534, 0.457], [-2.261, 2.755], [-1.457, 2.532], [-3.118, 1.362], [-3.335, 0.674]], "o": [[-0.974, -3.428], [3.209, -1.55], [3.534, -0.457], [1.854, -2.258], [1.697, -2.949], [3.118, -1.362], [0, 0]], "v": [[46.251, 25.333], [51.092, 16.772], [61.519, 14.664], [71.063, 10.453], [74.905, 2.609], [82.615, -3.814], [92.433, -6.532]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33.75, "s": [0]}, {"t": 47.5, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 20, "s": [0]}, {"t": 33.75, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.854901969433, 0.254901975393, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.083, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [0, 0], "to": [6.667, -3.333], "ti": [-6.667, 3.333]}, {"t": 47.5, "s": [40, -20]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.853, 7.248], [-7.332, 1.489], [-1.192, -7.386], [2.864, -1.24], [2.134, 2.953], [-3.689, 7.649], [-8.001, 2.847]], "o": [[-2.766, -6.952], [1.853, -7.248], [7.332, -1.489], [0.497, 3.081], [-3.343, 1.448], [-4.975, -6.883], [3.689, -7.649], [0, 0]], "v": [[39.367, 7.616], [37.046, -14.334], [51.842, -29.638], [68.839, -18.926], [65.401, -10.947], [56.144, -14.978], [54.614, -39.196], [73.501, -55.324]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.415686279535, 0.847058832645, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2.083, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 37.875, "s": [0]}, {"t": 51.625, "s": [100]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24.125, "s": [0]}, {"t": 37.875, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 3, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24.125, "s": [0, 0], "to": [6.667, -3.333], "ti": [-6.667, 3.333]}, {"t": 51.625, "s": [40, -20]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 53, "st": 20, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Layer 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [175, 175, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.86, 1.492]], "o": [[3.132, -2.705], [0, 0]], "v": [[-38.158, 29.636], [-27.565, 23.279]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.417, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.541, -0.258]], "o": [[0.541, 0.258], [0, 0]], "v": [[-56.612, 50.237], [-54.988, 51.01]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.458, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.008, -1.363]], "o": [[2.749, 1.831], [0, 0]], "v": [[-68.745, 43.735], [-60.091, 48.535]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.458, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.431, -0.211]], "o": [[0.431, 0.211], [0, 0]], "v": [[43.546, 48.685], [44.838, 49.317]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.458, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-1.795, -1.053], [-0.274, -0.437], [0.312, -0.578], [0.613, -0.235]], "o": [[1.795, 1.053], [0.445, 0.261], [0.348, 0.557], [-0.312, 0.578], [0, 0]], "v": [[48.438, 50.778], [53.821, 53.936], [55.002, 54.911], [55.007, 56.792], [53.522, 58.023]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.458, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.344, -1.863], [1.332, -1.347]], "o": [[1.725, 0.782], [0.344, 1.863], [0, 0]], "v": [[20.567, 42.848], [23.936, 47.152], [22.329, 52.376]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.776470601559, 0.776470601559, 0.75686275959, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.653, 2.539]], "o": [[5.292, 0.291], [0, 0]], "v": [[6.295, 58.794], [21.607, 55.334]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.776470601559, 0.776470601559, 0.75686275959, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-4.612, 0.164]], "o": [[4.497, 1.034], [0, 0]], "v": [[-8.298, 61.029], [5.444, 62.342]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.776470601559, 0.776470601559, 0.75686275959, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.14, 1.908], [-1.27, 3.932], [-1.895, 0.216]], "o": [[-3.527, -0.659], [-2.994, -1.819], [0.635, -1.968], [0, 0]], "v": [[-4.63, 57.164], [-14.901, 53.91], [-19.934, 43.432], [-15.687, 39.768]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.776470601559, 0.776470601559, 0.75686275959, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.042, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 4, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.861, -1.492], [2.714, -1.567], [0.861, 1.492], [-2.714, 1.567]], "o": [[0.861, 1.492], [-2.714, 1.567], [-0.861, -1.492], [2.714, -1.567]], "v": [[48.417, 66.058], [45.061, 71.596], [38.588, 71.733], [41.943, 66.194]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.886274516582, 0.870588243008, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.057, 0.851], [0, 0], [-2.02, -0.869], [0, 0], [1.98, -1.024], [0, 0], [2.37, 1.274], [0, 0]], "o": [[0, 0], [2.032, -0.841], [0, 0], [2.047, 0.88], [0, 0], [-2.39, 1.236], [0, 0], [-1.961, -1.054]], "v": [[-63.033, 33.384], [-29.73, 18.931], [-23.392, 18.975], [53.756, 53.464], [53.926, 58.233], [16.329, 76.902], [8.724, 76.842], [-63.274, 38.147]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.886274516582, 0.870588243008, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.981, 0.9], [0, 0], [-2.125, -0.938], [0, 0], [1.914, -1.054], [0, 0], [2.445, 1.338], [0, 0]], "o": [[0, 0], [2.115, -0.96], [0, 0], [1.999, 0.882], [0, 0], [-2.442, 1.345], [0, 0], [-1.908, -1.044]], "v": [[-80.564, 32.062], [-33.363, 10.623], [-26.699, 10.588], [72.627, 54.421], [72.835, 59.148], [20.583, 87.924], [12.736, 87.935], [-80.74, 36.78]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.415686279535, 0.40000000596, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.957, -0.527], [0, 0], [3.914, 2.142], [0, 0], [-0.209, 1.06], [0, 0]], "o": [[0, 0], [0.041, 0.94], [0, 0], [-3.909, 2.153], [0, 0], [-1.099, -0.601], [0, 0], [0, 0]], "v": [[74.13, 56.225], [74.198, 68.474], [72.835, 70.902], [22.938, 98.381], [10.377, 98.398], [-80.74, 48.534], [-82.061, 45.697], [-82.097, 34.757]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.854901969433, 0.254901975393, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 120, "st": 0, "bm": 0}], "markers": []}