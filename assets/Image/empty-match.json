{"v": "5.7.11", "fr": 25, "ip": 0, "op": 101, "w": 350, "h": 350, "nm": "Comp 16", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Layer 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [137.222, 130.859, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-37.778, -44.141, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 41, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 61.054, "s": [100, 100, 100]}, {"t": 94, "s": [0, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-36.402, -42.298], [-23.59, -44.141], [-36.402, -45.984], [-37.778, -63.308], [-39.153, -45.984], [-51.966, -44.141], [-39.153, -42.298], [-37.778, -24.974]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Layer 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [175, 175, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "ŝlosilo pli pura", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.421, 41.853], [-71.54, 24.557]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-50.55, 39.229], [-49.556, 24.317]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-60.447, 48.842], [-75.339, 47.845]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [0]}, {"t": 54, "s": [100]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-79.697, 68.33], "ix": 2}, "a": {"a": 0, "k": [-49.697, 48.33], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 35, "s": [0, 0]}, {"t": 43, "s": [100, 100]}], "ix": 3, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.421, 41.853], [-71.54, 24.557]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-50.55, 39.229], [-49.556, 24.317]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-60.447, 48.842], [-75.339, 47.845]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [0]}, {"t": 57, "s": [100]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-49.697, 48.33], "ix": 2}, "a": {"a": 0, "k": [-49.697, 48.33], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 38, "s": [0, 0]}, {"t": 46, "s": [100, 100]}], "ix": 3, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Layer 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [175, 175, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "ŝlosilo pli pura", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.421, 41.853], [-71.54, 24.557]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-50.55, 39.229], [-49.556, 24.317]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-60.447, 48.842], [-75.339, 47.845]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 43, "s": [0]}, {"t": 54, "s": [100]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-79.697, 68.33], "ix": 2}, "a": {"a": 0, "k": [-49.697, 48.33], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 35, "s": [0, 0]}, {"t": 43, "s": [100, 100]}], "ix": 3, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.421, 41.853], [-71.54, 24.557]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-50.55, 39.229], [-49.556, 24.317]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-60.447, 48.842], [-75.339, 47.845]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 0.380392163992, 0.380392163992, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.5, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 46, "s": [0]}, {"t": 57, "s": [100]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-49.697, 48.33], "ix": 2}, "a": {"a": 0, "k": [-49.697, 48.33], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667], "y": [1, 1]}, "o": {"x": [0.333, 0.333], "y": [0, 0]}, "t": 38, "s": [0, 0]}, {"t": 46, "s": [100, 100]}], "ix": 3, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Layer 6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [175, 275, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 100, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.734, -0.299], [0, 0], [-6.517, 5.143], [-1.196, 0.179], [-0.538, -0.12], [1.375, -0.597], [1.017, -1.555], [-0.659, -1.734], [-2.99, -0.538], [-2.034, -0.179], [-1.076, 0.419], [0.061, 1.136], [0.716, 0.9], [-1.196, -0.299], [-1.016, -0.598], [-6.099, 0.239]], "o": [[0, 0], [6.517, -5.142], [0.957, -0.718], [0.538, -0.12], [-0.598, 1.495], [-1.674, 0.718], [-1.017, 1.495], [1.016, 2.87], [1.973, 0.359], [1.136, 0.12], [1.076, -0.419], [0, -1.197], [-0.739, -0.929], [1.136, 0.299], [5.322, 3.11], [1.675, 0.478]], "v": [[8.34, 92.008], [-53.014, 90.752], [-33.46, 75.384], [-30.35, 73.71], [-28.736, 73.77], [-32.204, 76.221], [-36.45, 79.511], [-37.287, 84.653], [-29.872, 89.198], [-23.891, 90.035], [-20.483, 89.796], [-18.51, 87.225], [-20.902, 85.61], [-17.553, 84.892], [-14.384, 86.447], [3.257, 90.872]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058844566, 0.752941191196, 0.866666674614, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.734, -0.299], [0, 0], [-6.517, 5.143], [-1.196, 0.179], [-0.538, -0.12], [-0.897, -0.777], [-0.478, -0.419], [-4.425, -2.393], [-5.262, -1.315]], "o": [[0, 0], [6.517, -5.142], [0.957, -0.718], [0.538, -0.12], [1.076, 0.239], [0.479, 0.359], [3.827, 3.289], [4.784, 2.511], [1.675, 0.478]], "v": [[8.34, 92.008], [-53.014, 90.752], [-33.46, 75.384], [-30.35, 73.71], [-28.736, 73.77], [-25.806, 75.384], [-24.371, 76.521], [-11.932, 85.012], [3.257, 90.872]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.815686285496, 0.86274510622, 0.933333337307, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.155, -0.199], [0, 0], [-4.339, 3.424], [-0.796, 0.119], [-0.358, -0.08], [0.916, -0.398], [0.677, -1.035], [-0.439, -1.154], [-1.991, -0.358], [-1.354, -0.119], [-0.717, 0.279], [0.04, 0.757], [0.476, 0.599], [-0.796, -0.199], [-0.677, -0.398], [-4.061, 0.159]], "o": [[0, 0], [4.339, -3.424], [0.637, -0.478], [0.358, -0.08], [-0.398, 0.995], [-1.115, 0.478], [-0.677, 0.995], [0.677, 1.911], [1.314, 0.239], [0.756, 0.08], [0.717, -0.279], [0, -0.797], [-0.492, -0.618], [0.757, 0.199], [3.544, 2.07], [1.115, 0.318]], "v": [[70.475, 92.008], [29.626, 91.172], [42.645, 80.94], [44.715, 79.825], [45.79, 79.865], [43.481, 81.497], [40.654, 83.687], [40.097, 87.111], [45.034, 90.137], [49.015, 90.694], [51.285, 90.535], [52.598, 88.823], [51.006, 87.748], [53.235, 87.27], [55.345, 88.306], [67.091, 91.252]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058844566, 0.752941191196, 0.866666674614, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.155, -0.199], [0, 0], [-4.339, 3.424], [-0.796, 0.119], [-0.358, -0.08], [-0.597, -0.518], [-0.318, -0.279], [-2.946, -1.593], [-3.503, -0.876]], "o": [[0, 0], [4.339, -3.424], [0.637, -0.478], [0.358, -0.08], [0.717, 0.159], [0.319, 0.239], [2.548, 2.19], [3.185, 1.672], [1.115, 0.318]], "v": [[70.475, 92.008], [29.626, 91.172], [42.645, 80.94], [44.715, 79.825], [45.79, 79.865], [47.741, 80.94], [48.696, 81.697], [56.978, 87.35], [67.091, 91.252]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.815686285496, 0.86274510622, 0.933333337307, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.136, -2.628], [0, 0], [-3.305, -0.08], [-5.375, 1.673], [-1.354, 1.99], [0, 1.393]], "o": [[0, 0], [2.628, -2.031], [5.653, 0.119], [2.309, -0.756], [0.796, -1.155], [3.225, 4.738]], "v": [[51.723, 91.212], [5.498, 91.212], [15.014, 88.385], [31.855, 87.947], [37.708, 83.886], [38.942, 79.905]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.447058826685, 0.607843160629, 0.749019622803, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.041, -0.263], [0.527, -0.394], [0, 0], [0, 0], [-0.657, 0.92], [-1.229, 2.412], [-4.078, 2.016], [-0.657, 0.131], [-1.051, -4.778]], "o": [[-0.482, 0.527], [0, 0], [0, 0], [1.357, -0.569], [1.578, -2.192], [1.886, -3.769], [0.612, -0.306], [-1.884, 4.469], [1.271, 5.612]], "v": [[10.915, 89.791], [9.38, 91.193], [-12.451, 91.193], [-15.694, 91.105], [-10.828, 85.143], [-6.62, 78.217], [0.043, 65.329], [1.927, 64.716], [0.262, 79.049]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.447058826685, 0.607843160629, 0.749019622803, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.129, -2.629], [0, 0], [0, 0], [-0.657, 0.92], [-1.229, 2.412], [-4.078, 2.016], [-0.657, 0.131], [-1.271, 0.088], [-0.79, 0.088], [0.176, -3.288], [-3.155, -1.227], [-3.331, -0.351], [-3.814, -1.623], [-1.62, 4.208], [0.833, 2.367], [0, 0.088], [-1.141, -1.623]], "o": [[0, 0], [0, 0], [1.357, -0.569], [1.578, -2.192], [1.886, -3.769], [0.612, -0.306], [1.184, -0.308], [0.79, -0.043], [-2.586, 1.359], [-0.176, 3.814], [3.157, 1.271], [3.99, 0.394], [3.769, 1.623], [0.92, -2.325], [-0.045, -0.088], [1.49, 1.314], [3.243, 4.778]], "v": [[51.729, 91.193], [-12.451, 91.193], [-15.694, 91.105], [-10.828, 85.143], [-6.62, 78.217], [0.043, 65.329], [1.927, 64.716], [5.654, 64.233], [8.021, 64.057], [3.462, 71.686], [8.678, 80.496], [17.621, 77.341], [25.82, 87.029], [36.165, 82.909], [35.946, 75.455], [35.859, 75.149], [38.928, 79.884]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.647058844566, 0.752941191196, 0.866666674614, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-5.129, -2.629], [0, 0], [0, 0], [-0.045, 0.045], [0, 0], [-0.657, 0.92], [-1.229, 2.412], [-4.078, 2.016], [-0.657, 0.131], [-1.271, 0.088], [-0.79, 0.088], [-4.341, -2.761], [-2.631, -1.402], [-1.578, -0.92], [-0.263, -0.22], [-1.141, -1.623]], "o": [[0, 0], [0, 0], [0.045, 0], [0, 0], [1.357, -0.569], [1.578, -2.192], [1.886, -3.769], [0.612, -0.306], [1.184, -0.308], [0.79, -0.043], [5.216, -0.263], [2.498, 1.58], [1.578, 0.878], [0.351, 0.22], [1.49, 1.314], [3.243, 4.778]], "v": [[51.729, 91.193], [-12.451, 91.193], [-15.914, 91.15], [-15.739, 91.105], [-15.694, 91.105], [-10.828, 85.143], [-6.62, 78.217], [0.043, 65.329], [1.927, 64.716], [5.654, 64.233], [8.021, 64.057], [22.971, 66.818], [30.028, 72.168], [34.981, 74.49], [35.859, 75.149], [38.928, 79.884]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.815686285496, 0.86274510622, 0.933333337307, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-3.821, 0.152], [11.73, 0.68], [6.583, 0.379], [11.351, -0.682], [-5.864, -1.777], [7.15, -1.022], [10.217, 2.799], [1.437, 1.779], [0.19, 0.946], [-0.264, 0], [0, 0], [-2.346, 0.152], [-3.366, -0.113], [-7.113, 0.454], [-7.076, 1.02], [-11.616, -1.892], [-6.925, 0.53], [-4.39, 0.416], [-1.513, -0.037]], "o": [[-12.789, 2.346], [-6.544, -0.418], [-11.39, -0.719], [2.119, 0.076], [-7.074, -1.437], [-10.557, 1.513], [-2.195, -0.645], [-0.569, -0.719], [0.266, 0], [0, 0], [2.309, -0.188], [3.329, -0.19], [7.189, 0.303], [7.494, -0.454], [11.843, -1.703], [6.47, 1.022], [4.466, -0.379], [1.513, -0.19], [3.18, 1.931]], "v": [[72.506, 91.584], [37.657, 93.212], [18.134, 93.022], [-8.201, 92.53], [3.187, 95.86], [-34.158, 94.422], [-65.64, 97.223], [-71.504, 93.816], [-72.64, 91.244], [-71.883, 91.206], [-71.846, 91.206], [-64.958, 90.45], [-54.819, 90.411], [-33.289, 90.866], [-12.212, 87.764], [24.944, 88.065], [44.96, 90.184], [57.448, 88.179], [62.1, 87.989]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.003921568859, 0.556862771511, 0.603921592236, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.286, -0.985], [3.972, 0.342], [4.05, 0.188], [5.448, -0.87], [8.929, -0.379], [7.719, 1.703], [4.881, 0.227], [4.617, -0.794], [12.6, 0.567], [3.747, 1.059], [-1.325, 1.665], [-2.838, 0.266], [-2.308, -0.039], [-0.491, 0.037], [-0.264, 0], [0, 0], [-2.346, 0.152], [-3.366, -0.113], [-7.113, 0.454], [-7.076, 1.02], [-11.616, -1.892], [-6.925, 0.53], [-4.39, 0.416], [-1.513, -0.037], [-0.039, 0], [-5.033, -1.097], [-1.818, -0.833]], "o": [[-1.781, 1.323], [-3.972, -0.303], [-5.901, -0.264], [-8.211, 1.323], [-8.892, 0.379], [-4.199, -0.946], [-5.072, -0.227], [-11.54, 1.968], [-4.654, -0.188], [-3.745, -1.061], [0.833, -1.061], [2.422, -0.188], [0.491, 0], [0.266, 0], [0, 0], [2.309, -0.188], [3.329, -0.19], [7.189, 0.303], [7.494, -0.454], [11.843, -1.703], [6.47, 1.022], [4.466, -0.379], [1.513, -0.19], [0.039, 0], [5.637, 0.076], [2.535, 0.528], [1.814, 0.87]], "v": [[86.772, 95.331], [76.101, 96.654], [64.446, 95.368], [47.344, 96.996], [21.69, 99.871], [-4.305, 98.13], [-17.282, 95.671], [-31.813, 97.26], [-68.969, 99.416], [-82.176, 97.79], [-87.018, 93.212], [-80.775, 91.13], [-74.154, 91.32], [-72.64, 91.244], [-71.883, 91.206], [-71.846, 91.206], [-64.958, 90.45], [-54.819, 90.411], [-33.289, 90.866], [-12.212, 87.764], [24.944, 88.065], [44.96, 90.184], [57.448, 88.179], [62.1, 87.989], [62.178, 87.989], [78.41, 90.298], [85.372, 92.266]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.776470601559, 0.772549033165, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Layer 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [11]}, {"t": 17, "s": [0]}], "ix": 10, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "p": {"a": 0, "k": [165.343, 260.401, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-9.657, 85.401, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "ŝlosilo pli pura", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}, {"ty": 5, "nm": "ŝlosilo pli pura 2", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 2, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}, {"ty": 5, "nm": "ŝlosilo pli pura 3", "np": 34, "mn": "<PERSON><PERSON>udo/<PERSON> v3.2", "ix": 3, "en": 1, "ef": [{"ty": 7, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0001", "ix": 1, "v": {"a": 0, "k": 0, "ix": 1}}, {"ty": 7, "nm": "Smart Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}, {"ty": 7, "nm": "Follow Through", "mn": "P<PERSON>udo/<PERSON> v3.2-0003", "ix": 3, "v": {"a": 0, "k": 1, "ix": 3}}, {"ty": 6, "nm": "Anticipation", "mn": "P<PERSON>udo/<PERSON> v3.2-0004", "ix": 4, "v": 0}, {"ty": 0, "nm": "Duration (s)", "mn": "P<PERSON>udo/<PERSON> v3.2-0005", "ix": 5, "v": {"a": 0, "k": 0.3, "ix": 5}}, {"ty": 0, "nm": "Amplitude", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0006", "ix": 6, "v": {"a": 0, "k": 50, "ix": 6}}, {"ty": 6, "nm": "", "mn": "P<PERSON>udo/<PERSON> v3.2-0007", "ix": 7, "v": 0}, {"ty": 6, "nm": "Interpolation", "mn": "P<PERSON>udo/<PERSON> v3.2-0008", "ix": 8, "v": 0}, {"ty": 0, "nm": "Slow In", "mn": "P<PERSON>udo/<PERSON> v3.2-0009", "ix": 9, "v": {"a": 0, "k": 60, "ix": 9}}, {"ty": 0, "nm": "Slow Out", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0010", "ix": 10, "v": {"a": 0, "k": 25, "ix": 10}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0011", "ix": 11, "v": 0}, {"ty": 6, "nm": "Follow Through", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0012", "ix": 12, "v": 0}, {"ty": 0, "nm": "Elasticity", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0013", "ix": 13, "v": {"a": 0, "k": 10, "ix": 13}}, {"ty": 0, "nm": "Elasticity random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0014", "ix": 14, "v": {"a": 0, "k": 0, "ix": 14}}, {"ty": 0, "nm": "Damping", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0015", "ix": 15, "v": {"a": 0, "k": 50, "ix": 15}}, {"ty": 0, "nm": "Damping random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0016", "ix": 16, "v": {"a": 0, "k": 0, "ix": 16}}, {"ty": 7, "nm": "<PERSON><PERSON><PERSON>", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0017", "ix": 17, "v": {"a": 0, "k": 0, "ix": 17}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0018", "ix": 18, "v": 0}, {"ty": 6, "nm": "Spatial Options", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0019", "ix": 19, "v": 0}, {"ty": 7, "nm": "Smart Interpolation", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0020", "ix": 20, "v": {"a": 0, "k": 0, "ix": 20}}, {"ty": 7, "nm": "Mode", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0021", "ix": 21, "v": {"a": 0, "k": 1, "ix": 21}}, {"ty": 6, "nm": "Overlap (simulation)", "mn": "<PERSON><PERSON>udo/<PERSON><PERSON> v3.2-0022", "ix": 22, "v": 0}, {"ty": 7, "nm": "Overlap", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0023", "ix": 23, "v": {"a": 0, "k": 1, "ix": 23}}, {"ty": 0, "nm": "Delay (s)", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0024", "ix": 24, "v": {"a": 0, "k": 0.05, "ix": 24}}, {"ty": 0, "nm": "Overlap random", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0025", "ix": 25, "v": {"a": 0, "k": 0, "ix": 25}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0026", "ix": 26, "v": 0}, {"ty": 6, "nm": "Soft Body (simulation)", "mn": "<PERSON>seudo/<PERSON> v3.2-0027", "ix": 27, "v": 0}, {"ty": 7, "nm": "Soft Body", "mn": "<PERSON><PERSON>udo/<PERSON> v3.2-0028", "ix": 28, "v": {"a": 0, "k": 1, "ix": 28}}, {"ty": 0, "nm": "Soft-Body Flexibility", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0029", "ix": 29, "v": {"a": 0, "k": 100, "ix": 29}}, {"ty": 6, "nm": "", "mn": "<PERSON><PERSON><PERSON>/<PERSON><PERSON> v3.2-0030", "ix": 30, "v": 0}, {"ty": 6, "nm": "", "mn": "Pseudo/<PERSON><PERSON> v3.2-0031", "ix": 31, "v": 0}, {"ty": 0, "nm": "Precision", "mn": "Pseudo/<PERSON> v3.2-0032", "ix": 32, "v": {"a": 0, "k": 1, "ix": 32}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.792, 0.375], [0, 0], [1, 2.667], [0.042, 0.958], [-0.042, 1.25], [-0.667, 2.708], [-2.042, 1.917]], "o": [[0, 0], [-2.667, -0.917], [-0.334, -0.875], [-0.083, -1.25], [0.042, -2.792], [0.625, -2.75], [0.666, -0.583]], "v": [[0.896, -81.751], [0.896, -52.877], [-5.479, -58.085], [-5.979, -60.918], [-6.021, -64.626], [-5.228, -72.959], [-1.312, -80.251]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.847058832645, 0.501960813999, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.334, -0.042], [1.167, 0], [0, 0], [0, 1.417], [0, 0], [-1.416, 0], [0, 0], [-1, -1.833], [-3.166, -0.958], [-2.751, 0]], "o": [[-0.25, 1.125], [0, 0], [-1.416, 0], [0, 0], [0, -1.416], [0, 0], [0.042, 2.125], [1.583, 2.917], [2.667, 0.792], [0.333, 0]], "v": [[15.479, -87.167], [13.062, -85.209], [-6.063, -85.209], [-8.604, -87.75], [-8.604, -97.458], [-6.063, -100], [-2.813, -100], [-1.229, -94], [6.27, -87.917], [14.521, -87.209]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.792156875134, 0.156862750649, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.451], [0.956, -1.512], [2.425, 1.446], [0.867, 4.272], [-2.716, 0]], "o": [[0, 1.892], [-2.803, 0.335], [-3.759, -2.202], [1.78, -1.803], [5.452, 0]], "v": [[5.712, -38.712], [4.222, -33.527], [-3.923, -35.24], [-11.109, -45.676], [-4.145, -48.569]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.949019610882, 0.972549021244, 0.992156863213, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.451], [0.956, -1.512], [3.538, 0], [0, 5.452], [-1.782, 1.781], [-2.716, 0]], "o": [[0, 1.892], [-1.712, 2.804], [-5.43, 0], [0, -2.714], [1.78, -1.803], [5.452, 0]], "v": [[5.712, -38.712], [4.222, -33.527], [-4.145, -28.854], [-14.004, -38.712], [-11.109, -45.676], [-4.145, -48.569]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.815686285496, 0.86274510622, 0.933333337307, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.416], [0, 0], [0.042, -0.167], [1.167, 0], [0, 0], [0, 1.417], [0, 0], [-1.416, 0], [0, 0]], "o": [[0, 0], [0, 0.167], [-0.25, 1.125], [0, 0], [-1.416, 0], [0, 0], [0, -1.416], [0, 0], [1.375, 0]], "v": [[15.563, -97.458], [15.563, -87.667], [15.479, -87.167], [13.062, -85.209], [-6.063, -85.209], [-8.604, -87.75], [-8.604, -97.458], [-6.063, -100], [13.062, -100]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.847058832645, 0.501960813999, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.896], [1.034, -1.636], [3.826, 0], [0, 5.897], [-1.927, 1.926], [-2.938, 0]], "o": [[0, 2.046], [-1.852, 3.033], [-5.874, 0], [0, -2.936], [1.925, -1.95], [5.897, 0]], "v": [[6.517, -38.712], [4.905, -33.104], [-4.144, -28.049], [-14.808, -38.712], [-11.677, -46.245], [-4.144, -49.373]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.58, -0.89], [0, 0], [0, 0], [0, 0], [-1.712, -0.913]], "o": [[0, 0], [0, 0], [0, 0], [1.223, 1.535], [1.602, 0.845]], "v": [[0.906, -50.216], [0.906, -46.165], [-8.595, -46.165], [-8.595, -56.291], [-4.123, -52.552]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.001, -1.268], [0, 0], [2.425, -1.692], [0.422, -0.311], [0, 0], [-0.869, 0.089]], "o": [[0, 0], [-2.914, -0.222], [-0.422, 0.289], [0, 0], [0.845, -0.266], [2.403, -0.267]], "v": [[0.906, -95.654], [0.906, -84.328], [-7.35, -81.724], [-8.595, -80.812], [-8.595, -96.522], [-6.013, -97.056]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.917], [0, 0], [0.917, 0], [0, 0], [0, 0.917], [0, 0], [-0.875, 0], [0, 0]], "o": [[0, 0], [0, 0.917], [0, 0], [-0.875, 0], [0, 0], [0, -0.917], [0, 0], [0.917, 0]], "v": [[0.896, -98.125], [0.896, -47.793], [-0.729, -46.168], [-6.979, -46.168], [-8.604, -47.793], [-8.604, -98.125], [-6.979, -99.75], [-0.729, -99.75]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.792156875134, 0.156862750649, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 9", "np": 2, "cix": 2, "bm": 0, "ix": 9, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -1.403], [0, 0], [1.358, 0], [0, 0], [0, 1.38], [0, 0], [-1.38, 0]], "o": [[1.38, 0], [0, 0], [-0.043, 1.358], [0, 0], [-1.38, 0], [0, 0], [0, -1.403], [0, 0]], "v": [[13.785, -99.339], [16.299, -96.824], [16.299, -87.011], [13.785, -84.563], [-5.351, -84.563], [-7.865, -87.077], [-7.865, -96.824], [-5.351, -99.339]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 10", "np": 2, "cix": 2, "bm": 0, "ix": 10, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-3.815, -37.97], "ix": 2}, "a": {"a": 0, "k": [-3.815, -37.97], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10, "s": [90]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 74, "s": [0]}, {"t": 84, "s": [90]}], "ix": 6, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura 2');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura 2');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "sa": {"a": 0, "k": 0, "ix": 5, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura 2');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "nm": "Transform"}], "nm": "Group 1", "np": 10, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.597, -1.014], [-0.486, 1.02], [0.211, 0.647], [1.866, -3.363]], "o": [[1.185, 0.463], [0.293, -0.615], [-0.628, -1.923], [-1.112, 2.005]], "v": [[25.017, -67.768], [27.755, -68.404], [27.721, -70.396], [20.363, -74.401]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.035294119269, 0.364705890417, 0.643137276173, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.104, -1.603], [-0.768, 1.612], [0.334, 1.023], [2.949, -5.314]], "o": [[1.873, 0.732], [0.463, -0.971], [-0.993, -3.039], [-1.757, 3.168]], "v": [[24.026, -50.276], [28.353, -51.281], [28.3, -54.429], [16.672, -60.758]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.035294119269, 0.364705890417, 0.643137276173, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [3.066, 5.289], [6.109, -0.227]], "o": [[0.347, -6.104], [-3.066, -5.289], [0, 0]], "v": [[-31.12, -54.682], [-34.575, -72.428], [-50.507, -81.072]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0.833, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-2.975, -1.623], [0, 0], [2.434, 0], [0, 0], [0, 0], [2.075, 2.975], [0.812, 0.811], [4.869, 0.18], [0, 0], [0, 0], [0, 0], [-5.365, -8.52], [-18.437, -6.671]], "o": [[0, 0], [0, 2.434], [0, 0], [0, 0], [0, -3.922], [-0.631, -0.992], [-3.245, -3.246], [0, 0], [0, 0], [0, 0], [6.626, 7.122], [10.412, 16.679], [3.155, 1.172]], "v": [[47.088, -19.205], [47.088, -13.255], [42.67, -8.837], [-30.807, -8.657], [-30.807, -62.3], [-34.098, -72.848], [-36.306, -75.553], [-48.838, -81.007], [-48.883, -81.233], [-37.388, -81.278], [-25.577, -81.323], [-10.611, -54.907], [37.667, -23.262]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.035294119269, 0.364705890417, 0.643137276173, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.184], [0, 0], [2.434, 0], [0, 0], [0, 0], [0, 0], [2.075, 2.975], [0.812, 0.811], [4.869, 0.18], [0, 0], [0, 0], [0, 0], [0, 0], [-3.02, -3.02]], "o": [[0, 0], [0, 2.434], [0, 0], [0, 0], [0, 0], [0, -3.922], [-0.631, -0.992], [-3.245, -3.246], [0, 0], [0, 0], [0, 0], [0, 0], [4.508, 0.36], [3.381, 3.381]], "v": [[47.088, -62.48], [47.088, -13.255], [42.67, -8.837], [-30.13, -8.837], [-30.807, -11.452], [-30.807, -62.3], [-34.098, -72.848], [-36.306, -75.553], [-48.838, -81.007], [-48.883, -81.233], [-25.577, -81.323], [29.959, -81.413], [30.003, -81.142], [41.589, -75.733]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.156862750649, 0.713725507259, 0.956862747669, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -3.946], [0, 0], [4.718, -4.338], [1.245, -1.304], [0, 0], [-5.363, 0], [-1.758, -2.577]], "o": [[0, 0], [-5.517, 1.076], [-1.291, 1.174], [0, 0], [0, -7.892], [2.668, 0], [1.758, 2.577]], "v": [[1.383, -5.492], [1.383, -5.197], [-14.259, 2.988], [-18.064, 6.771], [-18.064, -5.492], [-8.34, -19.742], [-1.464, -15.568]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 6", "np": 2, "cix": 2, "bm": 0, "ix": 6, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -3.946], [0, 0], [2.158, -7.207], [0.513, -9.946], [3.338, -8.349], [0, 0], [-5.363, 0], [-1.758, -2.577]], "o": [[0, 0], [-4.961, 2.446], [-2.738, 9.131], [-0.511, 9.653], [0, 0], [0, -7.892], [2.668, 0], [1.758, 2.577]], "v": [[1.383, -5.492], [1.383, 0.835], [-9.809, 16.947], [-13.392, 46.265], [-18.064, 74.278], [-18.064, -5.492], [-8.34, -19.742], [-1.464, -15.568]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.623529434204, 0.109803922474, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 7", "np": 2, "cix": 2, "bm": 0, "ix": 7, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -3.946], [0, 0], [5.361, 0], [0, 7.86], [0, 0], [-5.363, 0], [-1.758, -2.577]], "o": [[0, 0], [0, 7.86], [-5.363, 0], [0, 0], [0, -7.892], [2.668, 0], [1.758, 2.577]], "v": [[1.383, -5.492], [1.383, 78.681], [-8.34, 92.901], [-18.064, 78.681], [-18.064, -5.492], [-8.34, -19.742], [-1.464, -15.568]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.792156875134, 0.156862750649, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 8", "np": 2, "cix": 2, "bm": 0, "ix": 8, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 8, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.281, -0.817], [0.127, 0.564], [0.65, 1.028], [0.836, 0.468], [2.532, -0.835], [1.165, 0.186], [0.156, 0.305], [-0.071, 0.311], [-0.127, 0.582], [-3.369, -1.499], [0.262, -3.723]], "o": [[-0.242, -0.494], [-0.279, -1.182], [-0.504, -0.817], [-2.325, -1.291], [-1.132, 0.389], [-0.337, -0.058], [-0.123, -0.271], [0.146, -0.575], [2.87, -2.35], [3.401, 1.534], [-0.057, 0.878]], "v": [[24.777, -55.326], [24.234, -56.944], [23.169, -60.418], [21.052, -62.329], [13.329, -63.041], [9.894, -62.249], [9.026, -62.751], [9.032, -63.671], [9.451, -65.403], [19.837, -66.848], [25.305, -57.894]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.795, -0.06], [3.422, -1.359], [0.762, 1.452], [2.068, 3.915], [0.29, 0.669], [-0.498, 3.029], [-1.117, 1.495], [-3.578, -1.059], [-0.952, -0.758], [0.3, -0.023], [0.968, -0.674], [0.861, -1.591], [0.289, -1.623], [-2.972, -3.172], [-3.037, -0.521]], "o": [[-3.441, 1.351], [-1.545, 0.594], [-2.043, -3.861], [-0.331, -0.618], [-1.228, -2.794], [0.303, -1.843], [2.233, -2.991], [1.172, 0.346], [-0.298, -0.043], [-1.162, 0.108], [-1.48, 1.024], [-0.762, 1.447], [-0.741, 4.287], [2.099, 2.263], [1.754, 0.292]], "v": [[12.872, -44.962], [2.635, -40.899], [-1.399, -42.421], [-7.501, -54.015], [-8.454, -55.919], [-9.548, -64.933], [-7.5, -70.083], [2.488, -73.294], [5.698, -71.607], [4.807, -71.623], [1.58, -70.305], [-1.966, -66.319], [-3.47, -61.664], [-0.626, -49.393], [7.543, -45.414]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.281, -0.817], [0.515, -0.843], [2.774, -1.307], [0.654, -0.251], [0.654, -0.252], [3.422, -1.359], [0.762, 1.452], [2.068, 3.915], [0.29, 0.669], [-0.498, 3.029], [-1.117, 1.495], [-3.578, -1.059], [-0.952, -0.758], [-0.02, -0.007], [-0.395, -2.439], [-0.007, 0.02], [-3.369, -1.499], [0.262, -3.723]], "o": [[-0.325, 0.935], [-1.601, 2.619], [-0.637, 0.325], [-0.654, 0.252], [-3.441, 1.351], [-1.545, 0.594], [-2.043, -3.861], [-0.331, -0.618], [-1.228, -2.794], [0.303, -1.843], [2.233, -2.991], [1.172, 0.346], [0, 0], [1.916, 1.544], [0.007, -0.02], [2.87, -2.35], [3.401, 1.534], [-0.057, 0.878]], "v": [[24.777, -55.326], [23.494, -52.657], [16.78, -46.544], [14.816, -45.723], [12.872, -44.962], [2.635, -40.899], [-1.399, -42.421], [-7.501, -54.015], [-8.454, -55.919], [-9.548, -64.933], [-7.5, -70.083], [2.488, -73.294], [5.698, -71.607], [5.718, -71.6], [9.416, -65.371], [9.451, -65.403], [19.837, -66.848], [25.305, -57.894]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.96862745285, 0.184313729405, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.496, -0.707], [0.531, 0.229], [1.214, 0.078], [0.87, -0.401], [0.813, -2.539], [0.835, -0.833], [0.338, 0.053], [0.21, 0.24], [0.397, 0.445], [-3.193, 1.845], [-2.859, -2.4]], "o": [[-0.543, -0.094], [-1.12, -0.469], [-0.957, -0.072], [-2.411, 1.122], [-0.35, 1.144], [-0.245, 0.239], [-0.292, -0.059], [-0.379, -0.456], [-0.215, -3.703], [3.24, -1.85], [0.676, 0.562]], "v": [[23.78, -39.623], [22.152, -40.135], [18.715, -41.315], [15.925, -40.725], [10.81, -34.896], [9.431, -31.651], [8.515, -31.244], [7.774, -31.789], [6.619, -33.146], [11.554, -42.398], [22.012, -41.56]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.103, 1.416], [0.912, -3.567], [1.622, 0.237], [4.383, 0.628], [0.712, 0.159], [2.158, 2.183], [0.554, 1.782], [-2.96, 2.272], [-1.173, 0.325], [0.157, -0.257], [0.024, -1.18], [-0.781, -1.631], [-1.143, -1.187], [-4.313, 0.54], [-2.207, 2.15]], "o": [[-0.929, 3.578], [-0.427, 1.599], [-4.324, -0.616], [-0.694, -0.095], [-2.982, -0.648], [-1.313, -1.328], [-1.107, -3.564], [0.969, -0.745], [-0.21, 0.216], [-0.596, 1.003], [-0.042, 1.799], [0.723, 1.467], [3.033, 3.119], [3.064, -0.368], [1.267, -1.247]], "v": [[25.167, -23.9], [22.438, -13.23], [18.836, -10.86], [5.869, -12.738], [3.768, -13.086], [-4.167, -17.499], [-7.13, -22.183], [-3.857, -32.151], [-0.606, -33.756], [-1.143, -33.045], [-1.974, -29.659], [-0.832, -24.448], [2.05, -20.495], [13.649, -15.583], [21.67, -19.854]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.496, -0.707], [-0.379, -0.912], [0.573, -3.012], [0.181, -0.677], [0.181, -0.677], [0.912, -3.567], [1.622, 0.237], [4.383, 0.628], [0.712, 0.159], [2.158, 2.183], [0.554, 1.782], [-2.96, 2.272], [-1.173, 0.325], [-0.018, 0.012], [-2.206, -1.114], [0.012, 0.018], [-3.193, 1.845], [-2.859, -2.4]], "o": [[0.566, 0.813], [1.178, 2.835], [-0.111, 0.706], [-0.181, 0.677], [-0.929, 3.578], [-0.427, 1.599], [-4.324, -0.616], [-0.694, -0.095], [-2.982, -0.648], [-1.313, -1.328], [-1.107, -3.564], [0.969, -0.745], [0, 0], [2.375, -0.643], [-0.012, -0.018], [-0.215, -3.703], [3.24, -1.85], [0.676, 0.562]], "v": [[23.78, -39.623], [25.185, -37.016], [26.185, -27.992], [25.694, -25.92], [25.167, -23.9], [22.438, -13.23], [18.836, -10.86], [5.869, -12.738], [3.768, -13.086], [-4.167, -17.499], [-7.13, -22.183], [-3.857, -32.151], [-0.606, -33.756], [-0.589, -33.768], [6.625, -33.099], [6.619, -33.146], [11.554, -42.398], [22.012, -41.56]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.96862745285, 0.184313729405, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [0, 0], "to": [-8.5, 0], "ti": [8.5, 0]}, {"t": 35, "s": [-51, 0]}], "ix": 2, "x": "var $bm_rt;\nvar fx = effect('ŝlosilo pli pura 3');\nvar doAnticipation = fx(1).value;\nvar doInterpolation = fx(2).value;\nvar doFollowThrough = fx(3).value;\nvar damping = $bm_div(fx(15).value, 10);\nvar elasticity = $bm_div(fx(13).value, 10);\nvar bounce = fx(17).value;\nvar anticipationDuration = fx(5).value;\nvar anticipationQuantity = $bm_div(fx(6).value, 100);\nvar slowIn = $bm_div(fx(9).value, 100);\nvar slowOut = $bm_div(fx(10).value, 100);\nvar spatialMode = fx(21).value;\nvar spatialDoInterpolation = fx(20).value;\nvar moBlurPrecision = fx(32).value;\nvar elasticityRandom = fx(14).value;\nvar dampingRandom = fx(16).value;\nvar softBody = fx(28).value;\nvar flexibility = $bm_div(fx(29).value, 100);\nvar doOverlap = fx(23).value;\nvar overlapDuration = fx(24).value;\nvar overlapRandom = fx(25).value;\nvar threshold = $bm_div($bm_div(1, moBlurPrecision), 1000);\nvar zeroValue;\nif ($bm_isInstanceOfArray(value)) {\n    if (value.length == 2)\n        zeroValue = [\n            0,\n            0\n        ];\n    else if (value.length == 3)\n        zeroValue = [\n            0,\n            0,\n            0\n        ];\n    else if (value.length == 4)\n        zeroValue = [\n            0,\n            0,\n            0,\n            0\n        ];\n} else\n    zeroValue = 0;\nvar isThisSpatial = isSpatial(thisProperty);\nvar isThisPosition = thisProperty === $bm_transform.position;\nvar simulate = false;\nif (isThisSpatial) {\n    doInterpolation = doInterpolation && spatialDoInterpolation;\n    if (isThisPosition && thisProperty.numKeys > 0) {\n        doOverlap = false;\n        simulate = false;\n    } else {\n        simulate = spatialMode == 2;\n        doOverlap = overlapDuration != 0 && doOverlap && simulate;\n    }\n} else {\n    doOverlap = false;\n}\nif (!doOverlap) {\n    overlapDuration = 0;\n}\nif (simulate && softBody && isThisSpatial && !isThisPosition && (doFollowThrough || doOverlap)) {\n    var distanceRatio = $bm_div(length(valueAtTime(0), $bm_transform.anchorPoint), $bm_div(thisLayer.width, 2));\n    distanceRatio = $bm_div($bm_sum(1, $bm_mul(distanceRatio, flexibility)), 2);\n    if (doFollowThrough) {\n        elasticity = $bm_div(elasticity, distanceRatio);\n        damping = $bm_div(damping, distanceRatio);\n    }\n    if (doOverlap) {\n        overlapDuration = $bm_mul(overlapDuration, distanceRatio);\n    }\n}\nseedRandom(0, true);\nif (doFollowThrough) {\n    if (elasticityRandom > 0)\n        elasticity = addNoise(elasticity, elasticityRandom);\n    if (dampingRandom > 0)\n        damping = addNoise(damping, dampingRandom);\n}\nif (doOverlap) {\n    if (doOverlap && overlapRandom > 0)\n        overlapDuration = addNoise(overlapDuration, overlapRandom);\n}\nvar result = value;\nfunction isSpatial(prop) {\n    if (!(prop.value instanceof Array))\n        return false;\n    if (prop.value.length != 2 && prop.value.length != 3)\n        return false;\n    try {\n        if (typeof prop.speed !== 'undefined')\n            return true;\n    } catch (e) {\n        return false;\n    }\n}\nfunction addNoise(val, quantity) {\n    var randomValue = random(0.9, 1.1);\n    var noiseValue = noise($bm_mul(valueAtTime(0), randomValue));\n    noiseValue = $bm_mul(noiseValue, $bm_div(quantity, 100));\n    return $bm_mul(val, $bm_sum(noiseValue, 1));\n}\nfunction isAfterLastKey() {\n    if (numKeys == 0)\n        return false;\n    var nKey = nearestKey(time);\n    return nKey.time <= time && nKey.index == numKeys;\n}\nfunction isStill(t, threshold) {\n    var d = $bm_sub(valueAtTime(t), valueAtTime($bm_sum(t, framesToTime(1))));\n    if ($bm_isInstanceOfArray(d)) {\n        for (var i = 0; i < d.length; i++) {\n            d[i] = Math.abs(d[i]);\n            if (d[i] >= threshold) {\n                return false;\n            }\n        }\n        return true;\n    } else {\n        d = Math.abs(d);\n        return d < threshold;\n    }\n}\nfunction bezierInterpolation(t, tMin, tMax, value1, value2, bezierPoints) {\n    if (arguments.length !== 5 && arguments.length !== 6)\n        return t;\n    var a = $bm_sub(value2, value1);\n    var b = $bm_sub(tMax, tMin);\n    if (b == 0)\n        return t;\n    var c = clamp($bm_div($bm_sub(t, tMin), b), 0, 1);\n    if (!(bezierPoints instanceof Array) || bezierPoints.length !== 4)\n        bezierPoints = [\n            0.33,\n            0,\n            0.66,\n            1\n        ];\n    return $bm_sum($bm_mul(a, h(c, bezierPoints)), value1);\n    function h(f, g) {\n        var x = $bm_mul(3, g[0]);\n        var j = $bm_sub($bm_mul(3, $bm_sub(g[2], g[0])), x);\n        var k = $bm_sub($bm_sub(1, x), j);\n        var l = $bm_mul(3, g[1]);\n        var m = $bm_sub($bm_mul(3, $bm_sub(g[3], g[1])), l);\n        var n = $bm_sub($bm_sub(1, l), m);\n        var d = f;\n        for (var i = 0; i < 5; i++) {\n            var z = $bm_sub($bm_mul(d, $bm_sum(x, $bm_mul(d, $bm_sum(j, $bm_mul(d, k))))), f);\n            if (Math.abs(z) < 0.001)\n                break;\n            d = $bm_sub(d, $bm_div(z, $bm_sum(x, $bm_mul(d, $bm_sum($bm_mul(2, j), $bm_mul($bm_mul(3, k), d))))));\n        }\n        return $bm_mul(d, $bm_sum(l, $bm_mul(d, $bm_sum(m, $bm_mul(d, n)))));\n    }\n}\nfunction getPropWorldSpeed(t, prop) {\n    return length(getPropWorldVelocity(t, prop));\n}\nfunction getPrevKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time <= t)\n        return nKey;\n    if (nKey.index > 1)\n        return key($bm_sub(nKey.index, 1));\n    return null;\n}\nfunction getNextKey(t) {\n    if (numKeys == 0)\n        return null;\n    var nKey = nearestKey(t);\n    if (nKey.time >= t)\n        return nKey;\n    if (nKey.index < numKeys)\n        return key($bm_sum(nKey.index, 1));\n    return null;\n}\nfunction getPropWorldVelocity(t, prop) {\n    return $bm_mul($bm_sub(getPropWorldValue($bm_sum(t, 0.005), prop), getPropWorldValue($bm_sub(t, 0.005), prop)), 100);\n}\nfunction getLayerWorldPos(t, l) {\n    return l.toWorld(l.anchorPoint, t);\n}\nfunction getPropWorldValue(t, prop) {\n    if (isPosition(prop))\n        return getLayerWorldPos(t, thisLayer);\n    return thisLayer.toWorld(prop.valueAtTime(t), t);\n}\nfunction isPosition(prop) {\n    return prop === $bm_transform.position;\n}\nfunction isKeyTop(k, axis) {\n    var prevSpeed = velocityAtTime($bm_sub(k.time, threshold));\n    var nextSpeed = velocityAtTime($bm_sum(k.time, threshold));\n    if ($bm_isInstanceOfArray(value)) {\n        prevSpeed = prevSpeed[axis];\n        nextSpeed = nextSpeed[axis];\n    }\n    if (Math.abs(prevSpeed) < 0.01 || Math.abs(nextSpeed) < 0.01)\n        return true;\n    return $bm_mul(prevSpeed, nextSpeed) < 0;\n}\nfunction anticipate() {\n    var anticipation = zeroValue;\n    if (isAfterLastKey())\n        return anticipation;\n    if (numKeys < 2)\n        return anticipation;\n    var nextKey = getNextKey(time);\n    var aKey = nextKey;\n    if (!isStill(aKey.time - 0.1, 0.1)) {\n        aKey = getPrevKey(time);\n        if (!isStill(aKey.time - 0.1, 0.1))\n            return anticipation;\n    }\n    if (aKey.index == numKeys)\n        return anticipation;\n    var anticipationMiddle = aKey.time;\n    var anticipationStart = $bm_sub(anticipationMiddle, anticipationDuration);\n    var anticipationEnd = key(aKey.index + 1).time;\n    var startValue = anticipation;\n    var midValue = $bm_mul($bm_sum($bm_neg(valueAtTime($bm_sum(anticipationMiddle, anticipationDuration))), aKey.value), anticipationQuantity);\n    var endValue = anticipation;\n    if (time < anticipationStart) {\n        return anticipation;\n    } else if (time < anticipationMiddle) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue[i], midValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationStart, anticipationMiddle, startValue, midValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else if (time <= anticipationEnd) {\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < value.length; i++) {\n                anticipation[i] = bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue[i], endValue[i], [\n                    slowOut,\n                    0,\n                    slowIn,\n                    1\n                ]);\n            }\n            return anticipation;\n        } else {\n            return bezierInterpolation(time, anticipationMiddle, anticipationEnd, midValue, endValue, [\n                slowOut,\n                0,\n                slowIn,\n                1\n            ]);\n        }\n    } else {\n        return anticipation;\n    }\n}\nfunction followThroughAtTime(t) {\n    var fThrough = zeroValue;\n    if (elasticity == 0)\n        return fThrough;\n    var propSpeed;\n    if (!simulate) {\n        if (numKeys < 2)\n            return fThrough;\n        if (nearestKey(t).index == 1)\n            return fThrough;\n        propSpeed = length(velocityAtTime(t));\n        if (propSpeed >= threshold)\n            return fThrough;\n    } else {\n        propSpeed = getPropWorldSpeed(t, thisProperty);\n        if (propSpeed >= threshold)\n            return fThrough;\n    }\n    var fThroughStart = 0;\n    var fThroughTime = 0;\n    if (simulate) {\n        var speedI = getPropWorldSpeed(t, thisProperty);\n        var i = t;\n        while (speedI < threshold && i > 0) {\n            i = $bm_sub(i, $bm_div(thisComp.frameDuration, moBlurPrecision));\n            speedI = getPropWorldSpeed(i, thisProperty);\n        }\n        fThroughStart = i;\n    } else {\n        var fThroughKey = getPrevKey(t);\n        fThroughStart = fThroughKey.time;\n    }\n    if (fThroughStart == 0)\n        return fThrough;\n    fThroughTime = $bm_sub(t, fThroughStart);\n    if (simulate)\n        fThrough = $bm_div(getPropWorldVelocity($bm_sub(fThroughStart, thisComp.frameDuration), thisProperty), 2);\n    else\n        fThrough = $bm_div(velocityAtTime($bm_sub(fThroughStart, thisComp.frameDuration)), 2);\n    if (bounce) {\n        var cycleDamp = Math.exp($bm_mul($bm_mul(fThroughTime, damping), 0.1));\n        var damp = $bm_div(Math.exp($bm_mul(fThroughTime, damping)), $bm_div(elasticity, 2));\n        var cycleDuration = $bm_div(1, $bm_mul(elasticity, 2));\n        cycleDuration = Math.round(timeToFrames(cycleDuration));\n        cycleDuration = framesToTime(cycleDuration);\n        var midDuration = $bm_div(cycleDuration, 2);\n        var maxValue = $bm_mul(fThrough, midDuration);\n        var cycvarime = fThroughTime;\n        var numEndCycles = 1;\n        while (cycvarime > cycleDuration) {\n            cycvarime = $bm_sub(cycvarime, cycleDuration);\n            cycleDuration = $bm_div(cycleDuration, cycleDamp);\n            cycleDuration = Math.round(timeToFrames(cycleDuration));\n            if (cycleDuration < 2) {\n                cycleDuration = 2;\n                numEndCycles++;\n            }\n            cycleDuration = framesToTime(cycleDuration);\n            midDuration = $bm_div(cycleDuration, 2);\n            maxValue = $bm_div($bm_mul(fThrough, midDuration), damp);\n            if (numEndCycles > 100 / damping && maxValue < threshold)\n                return zeroValue;\n        }\n        if (cycvarime < midDuration)\n            fThrough = bezierInterpolation(cycvarime, 0, midDuration, 0, maxValue, [\n                0,\n                0.1,\n                slowIn,\n                1\n            ]);\n        else\n            fThrough = bezierInterpolation(cycvarime, midDuration, cycleDuration, maxValue, 0, [\n                $bm_sub(1, slowIn),\n                0,\n                1,\n                0.9\n            ]);\n    } else {\n        var damp = Math.exp($bm_mul(fThroughTime, damping));\n        var sinus = $bm_mul($bm_mul($bm_mul(elasticity, fThroughTime), 2), Math.PI);\n        sinus = Math.sin(sinus);\n        sinus = $bm_mul($bm_div(0.3, elasticity), sinus);\n        sinus = $bm_div(sinus, damp);\n        if (Math.abs(sinus) < $bm_div(threshold, 100))\n            return 0;\n        fThrough = $bm_mul(fThrough, sinus);\n        if (threshold > 0) {\n            fThrough = $bm_mul(fThrough, $bm_sub(1, $bm_div(propSpeed, threshold)));\n        }\n    }\n    if (bounce) {\n        var prevValue = valueAtTime($bm_sub(fThroughStart, thisComp.frameDuration));\n        var startValue = valueAtTime(fThroughStart);\n        if ($bm_isInstanceOfArray(value)) {\n            for (var i = 0; i < prevValue.length; i++) {\n                if (prevValue[i] > startValue[i])\n                    fThrough[i] = Math.abs(fThrough[i]);\n                if (prevValue[i] < startValue[i])\n                    fThrough[i] = $bm_neg(Math.abs(fThrough[i]));\n            }\n        } else {\n            if (prevValue > startValue)\n                fThrough = Math.abs(fThrough);\n            if (prevValue < startValue)\n                fThrough = $bm_neg(Math.abs(fThrough));\n        }\n    }\n    if (simulate) {\n        if (!isThisPosition) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer));\n            fThrough = $bm_sub(thisLayer.fromWorld(fThrough), thisLayer.anchorPoint);\n        } else if (thisLayer.hasParent) {\n            fThrough = $bm_sum(fThrough, getLayerWorldPos(time, thisLayer.parent));\n            fThrough = $bm_sub(thisLayer.parent.fromWorld(fThrough), thisLayer.parent.anchorPoint);\n        }\n    }\n    return fThrough;\n}\nfunction followThrough() {\n    var propSpeed = length(velocity);\n    if (propSpeed < threshold)\n        return followThroughAtTime($bm_sub(time, overlapDuration));\n    var fThrough = zeroValue;\n    var t = time;\n    while (t > 0) {\n        t = $bm_sub(t, thisComp.frameDuration);\n        if (simulate)\n            propSpeed = getPropWorldSpeed($bm_sub(t, overlapDuration), thisProperty);\n        else\n            propSpeed = length(velocityAtTime(t));\n        if (propSpeed < threshold) {\n            fThrough = followThroughAtTime($bm_sub(t, overlapDuration));\n            break;\n        }\n    }\n    return linear(time, t, $bm_sum(t, $bm_mul(anticipationDuration, 2)), fThrough, zeroValue);\n}\nfunction smartSmooth(axis) {\n    var startKey = nearestKey(time);\n    var endKey = startKey;\n    if (time == startKey.time)\n        return 0;\n    if (time < startKey.time && startKey.index == 1)\n        return 0;\n    if (time > startKey.time && startKey.index == numKeys)\n        return 0;\n    if (time < startKey.time)\n        startKey = key($bm_sub(startKey.index, 1));\n    if (time > startKey.time)\n        endKey = key($bm_sum(startKey.index, 1));\n    var sI = 0.66;\n    var sO = 0.33;\n    var sIV = 1;\n    var sOV = 0;\n    var sVal = startKey.value;\n    var eVal = endKey.value;\n    if ($bm_isInstanceOfArray(value)) {\n        sVal = sVal[axis];\n        eVal = eVal[axis];\n    }\n    var sTime = startKey.time;\n    var eTime = endKey.time;\n    if (isKeyTop(startKey, axis))\n        sO = slowOut;\n    else {\n        var prevKey = key($bm_sub(startKey.index, 1));\n        var pVal = prevKey.value;\n        if ($bm_isInstanceOfArray(value))\n            pVal = pVal[axis];\n        sOV = $bm_div($bm_sub(sVal, pVal), $bm_sub(eVal, pVal));\n    }\n    if (isKeyTop(endKey, axis)) {\n        sI = slowIn;\n        if (endKey.index != numKeys) {\n            var nextKey = key($bm_sum(endKey.index, 1));\n            var nVal = nextKey.value;\n            if ($bm_isInstanceOfArray(value))\n                nVal = nVal[axis];\n            if (Math.abs(nVal - eVal) < 0.01 && doFollowThrough)\n                sI = 1;\n        }\n    } else {\n        var nextKey = key($bm_sum(endKey.index, 1));\n        var nVal = nextKey.value;\n        if ($bm_isInstanceOfArray(value))\n            nVal = nVal[axis];\n        sIV = $bm_div($bm_sub(eVal, sVal), $bm_sub(nVal, sVal));\n    }\n    if (endKey.index == numKeys && doFollowThrough) {\n        sI = 1;\n    }\n    var val = value;\n    if ($bm_isInstanceOfArray(value))\n        val = val[axis];\n    return $bm_sub(bezierInterpolation(time, sTime, eTime, sVal, eVal, [\n        sO,\n        sOV,\n        sI,\n        sIV\n    ]), val);\n}\nfunction overlap() {\n    var ol = zeroValue;\n    if (isThisPosition && !hasParent)\n        return zeroValue;\n    ol = $bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisProperty), getPropWorldValue(time, thisProperty));\n    var motionRatio = $bm_div($bm_div(length(zeroValue, ol), thisLayer.width), 2);\n    if (isThisPosition) {\n        var originalDistance = length(valueAtTime(0));\n        motionRatio = $bm_div(length(zeroValue, ol), thisComp.width);\n    }\n    ol = $bm_sum(ol, getPropWorldValue(time, thisProperty));\n    ol = $bm_sum(ol, $bm_mul($bm_mul($bm_sub(getPropWorldValue($bm_sub(time, overlapDuration), thisLayer.anchorPoint), ol), motionRatio), flexibility));\n    ol = thisLayer.fromWorld(ol);\n    if (!isThisPosition)\n        ol = $bm_sub(ol, value);\n    else {\n        ol = linear(flexibility, 0, 100, $bm_div(ol, 2), 0);\n        var prevParentWorldPos = getLayerWorldPos($bm_sub(time, overlapDuration), parent);\n        ol = $bm_sum(ol, $bm_mul($bm_mul($bm_mul(thisLayer.fromWorld(prevParentWorldPos), motionRatio), flexibility), 5));\n    }\n    return ol;\n}\nvar okToGo = false;\nif (simulate && fx.enabled)\n    okToGo = true;\nelse if (numKeys > 1 && fx.enabled)\n    okToGo = true;\nif (okToGo) {\n    var smartSmoothResult = zeroValue;\n    if (doInterpolation) {\n        if ($bm_isInstanceOfArray(value)) {\n            if (value.length == 2)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1)\n                ];\n            else if (value.length == 3)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2)\n                ];\n            else if (value.length == 4)\n                smartSmoothResult = [\n                    smartSmooth(0),\n                    smartSmooth(1),\n                    smartSmooth(2),\n                    smartSmooth(3)\n                ];\n        } else {\n            smartSmoothResult = smartSmooth(0);\n        }\n    }\n    if (doAnticipation)\n        result = $bm_sum(result, anticipate());\n    result = $bm_sum(result, smartSmoothResult);\n    if (doFollowThrough)\n        result = $bm_sum(result, followThrough());\n    if (doOverlap)\n        result = $bm_sum(result, overlap());\n}\n$bm_rt = result;"}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.281, -0.817], [0.127, 0.564], [0.65, 1.028], [0.836, 0.468], [2.532, -0.835], [1.165, 0.186], [0.156, 0.305], [-0.071, 0.311], [-0.127, 0.582], [-3.369, -1.499], [0.262, -3.723]], "o": [[-0.242, -0.494], [-0.279, -1.182], [-0.504, -0.817], [-2.325, -1.291], [-1.132, 0.389], [-0.337, -0.058], [-0.123, -0.271], [0.146, -0.575], [2.87, -2.35], [3.401, 1.534], [-0.057, 0.878]], "v": [[-25.256, -55.326], [-25.799, -56.944], [-26.864, -60.418], [-28.982, -62.329], [-36.704, -63.041], [-40.139, -62.249], [-41.007, -62.751], [-41.002, -63.671], [-40.583, -65.403], [-30.197, -66.848], [-24.728, -57.894]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.795, -0.06], [3.422, -1.359], [0.762, 1.452], [2.068, 3.915], [0.29, 0.669], [-0.498, 3.029], [-1.117, 1.495], [-3.578, -1.059], [-0.952, -0.758], [0.3, -0.023], [0.968, -0.674], [0.861, -1.591], [0.289, -1.623], [-2.972, -3.172], [-3.037, -0.521]], "o": [[-3.441, 1.351], [-1.545, 0.594], [-2.043, -3.861], [-0.331, -0.618], [-1.228, -2.794], [0.303, -1.843], [2.233, -2.991], [1.172, 0.346], [-0.298, -0.043], [-1.162, 0.108], [-1.48, 1.024], [-0.762, 1.447], [-0.741, 4.287], [2.099, 2.263], [1.754, 0.292]], "v": [[-37.162, -44.962], [-47.399, -40.899], [-51.433, -42.421], [-57.535, -54.015], [-58.488, -55.919], [-59.581, -64.933], [-57.534, -70.083], [-47.545, -73.294], [-44.336, -71.607], [-45.227, -71.623], [-48.454, -70.305], [-51.999, -66.319], [-53.504, -61.664], [-50.66, -49.393], [-42.491, -45.414]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.281, -0.817], [0.515, -0.843], [2.774, -1.307], [0.654, -0.251], [0.654, -0.252], [3.422, -1.359], [0.762, 1.452], [2.068, 3.915], [0.29, 0.669], [-0.498, 3.029], [-1.117, 1.495], [-3.578, -1.059], [-0.952, -0.758], [-0.02, -0.007], [-0.395, -2.439], [-0.007, 0.02], [-3.369, -1.499], [0.262, -3.723]], "o": [[-0.325, 0.935], [-1.601, 2.619], [-0.637, 0.325], [-0.654, 0.252], [-3.441, 1.351], [-1.545, 0.594], [-2.043, -3.861], [-0.331, -0.618], [-1.228, -2.794], [0.303, -1.843], [2.233, -2.991], [1.172, 0.346], [0, 0], [1.916, 1.544], [0.007, -0.02], [2.87, -2.35], [3.401, 1.534], [-0.057, 0.878]], "v": [[-25.256, -55.326], [-26.54, -52.657], [-33.253, -46.544], [-35.218, -45.723], [-37.162, -44.962], [-47.399, -40.899], [-51.433, -42.421], [-57.535, -54.015], [-58.488, -55.919], [-59.581, -64.933], [-57.534, -70.083], [-47.545, -73.294], [-44.336, -71.607], [-44.316, -71.6], [-40.617, -65.371], [-40.583, -65.403], [-30.197, -66.848], [-24.728, -57.894]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.96862745285, 0.184313729405, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [0, 0], "to": [-5.013, 0.931], "ti": [3.352, -16.568]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [-30.08, 5.585], "to": [-3.352, 16.568], "ti": [-1.662, -15.637]}, {"t": 39, "s": [-20.11, 99.405]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 39, "s": [100, 100]}, {"t": 42, "s": [0, 0]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.496, -0.707], [0.531, 0.229], [1.214, 0.078], [0.87, -0.401], [0.813, -2.539], [0.835, -0.833], [0.338, 0.053], [0.21, 0.24], [0.397, 0.445], [-3.193, 1.845], [-2.859, -2.4]], "o": [[-0.543, -0.094], [-1.12, -0.469], [-0.957, -0.072], [-2.411, 1.122], [-0.35, 1.144], [-0.245, 0.239], [-0.292, -0.059], [-0.379, -0.456], [-0.215, -3.703], [3.24, -1.85], [0.676, 0.562]], "v": [[-26.254, -39.623], [-27.882, -40.135], [-31.319, -41.315], [-34.109, -40.725], [-39.224, -34.896], [-40.603, -31.651], [-41.519, -31.244], [-42.26, -31.789], [-43.415, -33.146], [-38.48, -42.398], [-28.021, -41.56]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.103, 1.416], [0.912, -3.567], [1.622, 0.237], [4.383, 0.628], [0.712, 0.159], [2.158, 2.183], [0.554, 1.782], [-2.96, 2.272], [-1.173, 0.325], [0.157, -0.257], [0.024, -1.18], [-0.781, -1.631], [-1.143, -1.187], [-4.313, 0.54], [-2.207, 2.15]], "o": [[-0.929, 3.578], [-0.427, 1.599], [-4.324, -0.616], [-0.694, -0.095], [-2.982, -0.648], [-1.313, -1.328], [-1.107, -3.564], [0.969, -0.745], [-0.21, 0.216], [-0.596, 1.003], [-0.042, 1.799], [0.723, 1.467], [3.033, 3.119], [3.064, -0.368], [1.267, -1.247]], "v": [[-24.866, -23.9], [-27.596, -13.23], [-31.198, -10.86], [-44.165, -12.738], [-46.266, -13.086], [-54.201, -17.499], [-57.164, -22.183], [-53.891, -32.151], [-50.64, -33.756], [-51.177, -33.045], [-52.007, -29.659], [-50.865, -24.448], [-47.984, -20.495], [-36.384, -15.583], [-28.364, -19.854]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.886274516582, 0.129411771894, 0.180392161012, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.496, -0.707], [-0.379, -0.912], [0.573, -3.012], [0.181, -0.677], [0.181, -0.677], [0.912, -3.567], [1.622, 0.237], [4.383, 0.628], [0.712, 0.159], [2.158, 2.183], [0.554, 1.782], [-2.96, 2.272], [-1.173, 0.325], [-0.018, 0.012], [-2.206, -1.114], [0.012, 0.018], [-3.193, 1.845], [-2.859, -2.4]], "o": [[0.566, 0.813], [1.178, 2.835], [-0.111, 0.706], [-0.181, 0.677], [-0.929, 3.578], [-0.427, 1.599], [-4.324, -0.616], [-0.694, -0.095], [-2.982, -0.648], [-1.313, -1.328], [-1.107, -3.564], [0.969, -0.745], [0, 0], [2.375, -0.643], [-0.012, -0.018], [-0.215, -3.703], [3.24, -1.85], [0.676, 0.562]], "v": [[-26.254, -39.623], [-24.849, -37.016], [-23.849, -27.992], [-24.34, -25.92], [-24.866, -23.9], [-27.596, -13.23], [-31.198, -10.86], [-44.165, -12.738], [-46.266, -13.086], [-54.201, -17.499], [-57.164, -22.183], [-53.891, -32.151], [-50.64, -33.756], [-50.622, -33.768], [-43.409, -33.099], [-43.415, -33.146], [-38.48, -42.398], [-28.021, -41.56]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.96862745285, 0.184313729405, 0.262745112181, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 17, "s": [0, 0], "to": [-5.013, 0.931], "ti": [3.352, -16.568]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [-30.08, 5.585], "to": [-3.352, 16.568], "ti": [-1.662, -15.637]}, {"t": 35, "s": [-20.11, 99.405]}], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 35, "s": [100, 100]}, {"t": 38, "s": [0, 0]}], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.397], [0, 0], [0, 0], [0, 0], [-3.291, 3.381], [-5.274, 0], [-1.172, -0.27]], "o": [[0, 0], [0, 0], [0, 0], [0, -5.094], [3.426, -3.471], [1.262, 0], [0.316, 1.307]], "v": [[40.81, -62.299], [40.81, -8.656], [-53.703, -8.656], [-53.703, -48.009], [-48.384, -61.127], [36.707, -66.762], [40.359, -66.356]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.397], [0, 0], [0, 0], [0, 0], [-3.291, 3.336], [-5.274, 0], [-2.795, -1.713], [-0.54, -2.389]], "o": [[0, 0], [0, 0], [0, 0], [0, -5.094], [3.426, -3.516], [3.471, 0], [1.351, 1.938], [0.316, 1.307]], "v": [[40.81, -62.299], [40.81, -8.656], [-62.447, -8.656], [-62.447, -56.754], [-57.129, -69.827], [-43.65, -75.507], [37.519, -72.847], [40.359, -66.356]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.098039217293, 0.203921571374, 0.364705890417, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -3.922], [0, 0], [0, 0], [0, 2.434], [0, 0], [-3.291, 3.336], [-5.274, 0], [-0.27, -0.045], [-3.245, -3.246], [-0.631, -0.992]], "o": [[0, 0], [0, 0], [-2.434, 0], [0, 0], [0, -5.094], [3.426, -3.516], [0.271, 0], [4.869, 0.18], [0.812, 0.811], [2.075, 2.975]], "v": [[40.805, -62.3], [40.805, -8.657], [-63.984, -8.657], [-68.401, -13.075], [-68.401, -62.3], [-63.082, -75.373], [-49.604, -81.053], [-48.838, -81.007], [35.306, -75.553], [37.515, -72.848]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.035294119269, 0.364705890417, 0.643137276173, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 251, "st": 0, "bm": 0}], "markers": []}