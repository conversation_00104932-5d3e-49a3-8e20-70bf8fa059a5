<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>GoMeet</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>gomeet</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>GADApplicationIdentifier</key>
    <string>ca-app-pub-3940256099942544~1458002511</string>
        <key>NSLocationWhenInUseUsageDescription</key>
        <string>This application requires location services to work</string>
        <key>NSLocationAlwaysUsageDescription</key>
        <string>This application requires location services to work</string>

        <key>NSPhotoLibraryUsageDescription</key>
        <string>Please grant photo gallery access</string>
        <key>FacebookAdvertiserIDCollectionEnabled</key>
    	<false/>
    	<key>FacebookDisplayName</key>
        	<string>User Authentication App</string>
        	<key>LSApplicationQueriesSchemes</key>
        	<array>
        		<string>fbapi</string>
        		<string>fb-messenger-share-api</string>
        	</array>
    	
    	<key>NSContactsUsageDescription</key>
        <string>$(NSContactsUsageDescription)</string>
    	<key>FLTEnableImpeller</key>
    	<true/>
    	
    	<key>FirebaseAppDelegateProxyEnabled</key>
    	<false/>
       
          <key>NSCameraUsageDescription</key>
          <string>We need access to the camera to take pictures.</string>
          <key>NSMicrophoneUsageDescription</key>
          <string>We need access to the microphone for recording audio.</string>
</dict>
</plist>
