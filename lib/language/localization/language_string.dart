// ignore_for_file: file_names

import 'package:get/get.dart';

class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
    'en_English'   : {
      "Please don`t press back until the transaction is complete" : "Please don`t press back until the transaction is complete",
      "SUCCESS PAYMENT" : "SUCCESS PAYMENT",
      "Something went wrong," : "Something went wrong,",
      "Tap to retry" : "Tap to retry",
      "Payment Completed" : "Payment Completed",
      "Inappropriate Content" : "Inappropriate Content",
      "Violation of Terms" : "Violation of Terms",
      "Offensive Language" : "Offensive Language",
      "Disrespectful Behavior" : "Disrespectful Behavior",
      "Threats" : "Threats",
      "Catfishing" : "Catfishing",
      "Unwanted Advances" : "Unwanted Advances",
      "Unsolicited Explicit Content" : "Unsolicited Explicit Content",
      "Privacy Concerns" : "Privacy Concerns",
      "Scam or Spam" : "Scam or Spam",
      "Something else" : "Something else",
      "They will not be able to find your profile and send you messages." : "They will not be able to find your profile and send you messages.",
      "Blocking" : "Blocking",
      "They will not be notified if you block them." : "They will not be notified if you block them.",
      "You can unblock them anytime in Settings." : "You can unblock them anytime in Settings.",
      "Cancel" : "Cancel",
      "Yes, Block" : "Yes, Block",
      "Block" : "Block",
      "Why did you report this user?" : "Why did you report this user?",
      "Reporting" : "Reporting",
      "Continue" : "Continue",
      "Report" : "Report",
      "Something Want Wrong" : "Something Want Wrong",
      "Say Something.." : "Say Something..",
      "No messages here yet..." : "No messages here yet...",
      "Send a message or tap on the greeting below" : "Send a message or tap on the greeting below",
      "Please wait for user to join" : "Please wait for user to join",
      "Ringing.." : "Ringing..",
      "Sign in" : "Sign in",
      "Welcome back! Please enter your details." : "Welcome back! Please enter your details.",
      "Email or MobileNumber" : "Email or MobileNumber",
      "Password" : "Password",
      "Forgot password? " : "Forgot password? ",
      "Reset it" : "Reset it",
      "Enter Number" : "Enter Number",
      "Mobile Number" : "Mobile Number",
      "Please enter your mobile number" : "Please enter your mobile number",
      "Number Not Exist" : "Number Not Exist",
      "Sign In" : "Sign In",
      "Connect with Google" : "Connect with Google",
      "Connect with Facebook" : "Connect with Facebook",
      "Connect with Apple" : "Connect with Apple",
      "You’ve seen everyone in your filters" : "You’ve seen everyone in your filters",
      "To give you another chance, we’re showing you everyone again." : "To give you another chance, we’re showing you everyone again.",
      "We’ll always show you new and unseen profiles first." : "We’ll always show you new and unseen profiles first.",
      "Unverify" : "Unverify",
      "Verify" : "Verify",
      "Reset" : "Reset",
      "Apply" : "Apply",
      "Filter & Show" : "Filter & Show",
      "Distance Range" : "Distance Range",
      "KM" : "KM",
      "Age Range" : "Age Range",
      "Search Preference" : "Search Preference",
      "Interests" : "Interests",
      "Languages I Know" : "Languages I Know",
      "Religion" : "Religion",
      "Relationship Goals" : "Relationship Goals",
      "Verify Profile" : "Verify Profile",
      "Explore" : "Explore",
      "Match" : "Match",
      "Away" : "Away",
      "UNLIKE" : "UNLIKE",
      "LIKE" : "LIKE",
      "Home" : "Home",
      "Maps" : "Maps",
      "Chats" : "Chats",
      "Profile" : "Profile",
      "Search.." : "Search..",
      "No contact, yet." : "No contact, yet.",
      "No messages in your inbox.Start chatting with people around you." : "No messages in your inbox.Start chatting with people around you.",
      "User Not Found" : "User Not Found",
      "User granted permission" : "User granted permission",
      "User granted provisional permission" : "User granted provisional permission",
      "User declined or has not accepted permission" : "User declined or has not accepted permission",
      "Hello" : "Hello",
      "No New Profiles" : "No New Profiles",
      "Change your preferences to expand your search and see new profiles." : "Change your preferences to expand your search and see new profiles.",
      "Change my preferences" : "Change my preferences",
      "Refresh" : "Refresh",
      "Premium" : "Premium",
      "Your admirers haven't noticed you yet" : "Your admirers haven't noticed you yet",
      "Find here people who are intrested in you react on their profile for Crusher and to disccus in the maintime, put all the chances on your side with a great profile!" : "Find here people who are intrested in you react on their profile for Crusher and to disccus in the maintime, put all the chances on your side with a great profile!",
      "Improve my profile" : "Improve my profile",
      "Boost your profile" : "Boost your profile",
      "Location (Within " : "Location (Within ",
      " km)" : " km)",
      "Edit" : "Edit",
      "Notifications" : "Notifications",
      "Update" : "Update",
      "Nick name" : "Nick name",
      "First Name" : "First Name",
      "Email" : "Email",
      "Radius" : "Radius",
      " KM" : " KM",
      "Birthdate" : "Birthdate",
      "Bio" : "Bio",
      "Gender" : "Gender",
      "Ok" : "Ok",
      "Height" : "Height",
      "New" : "New",
      "Submit" : "Submit",
      "Here is a chance to add height to your profile" : "Here is a chance to add height to your profile",
      "cm" : "cm",
      "Awesome" : "Awesome",
      "OTP Invalid" : "OTP Invalid",
      "You and " : "You and ",
      " liked each other!" : " liked each other!",
      "Send a message" : "Send a message",
      "Keep Swiping" : "Keep Swiping",
      "Upgrade" : "Upgrade",
      "You're Activated Membership!" : "You're Activated Membership!",
      "Payment method" : "Payment method",
      "transaction id" : "transaction id",
      "Date of Purchase" : "Date of Purchase",
      "Date of Expiry" : "Date of Expiry",
      "Membership Amount" : "Membership Amount",
      "Not Valid" : "Not Valid",
      "Payment Successfully" : "Payment Successfully",
      "Select Plan" : "Select Plan",
      "Active" : "Active",
      "Add Your payment information" : "Add Your payment information",
      "What number is written on card?" : "What number is written on card?",
      "Number" : "Number",
      "Number behind the card" : "Number behind the card",
      "CVV" : "CVV",
      "MM/YY" : "MM/YY",
      "Expiry Date" : "Expiry Date",
      "Please fix the errors in red before submitting." : "Please fix the errors in red before submitting.",
      "Payment card is valid" : "Payment card is valid",
      "Something went Wrong....!!!" : "Something went Wrong....!!!",
      "From where do you want to take the photo?" : "From where do you want to take the photo?",
      "Unblock" : "Unblock",
      "Faq" : "Faq",
      "Get Photo Verified" : "Get Photo Verified",
      "We want to know it`s really you." : "We want to know it`s really you.",
      "Tack a quick video selfie" : "Tack a quick video selfie",
      "Confirm you`re the person in your photos." : "Confirm you`re the person in your photos.",
      "Before you continue..." : "Before you continue...",
      "Prep your lighting" : "Prep your lighting",
      "Choose a well-lit environment" : "Choose a well-lit environment",
      "Turn up your brightness" : "Turn up your brightness",
      "Show your face" : "Show your face",
      "Face the camera directly" : "Face the camera directly",
      "Remove hats, sunglasses, and face coverings" : "Remove hats, sunglasses, and face coverings",
      "Get ready for" : "Get ready for",
      "your image selfie" : "your image selfie",
      "Make sure to frame your face in the oval, then tap  'i`m ready'!" : "Make sure to frame your face in the oval, then tap  'i`m ready'!",
      "i m ready" : "i m ready",
      "Maybe Later" : "Maybe Later",
      "verification Under" : "verification Under",
      "We are currently reviewing your selfies and will get back to you shortly!" : "We are currently reviewing your selfies and will get back to you shortly!",
      "OKAY" : "OKAY",
      "Gallery" : "Gallery",
      "did not pick an image!!" : "did not pick an image!!",
      "Camera" : "Camera",
      "Join Our Membership Today!" : "Join Our Membership Today!",
      "Enjoy  premium and match anywhere." : "Enjoy  premium and match anywhere.",
      "Checkout GoMeet Premium" : "Checkout GoMeet Premium",
      "Go" : "Go",
      "Profile & Privacy" : "Profile & Privacy",
      "The people you blocked are displayed here." : "The people you blocked are displayed here.",
      "Push Notifications (4)" : "Push Notifications (4)",
      "Dark Mode" : "Dark Mode",
      "Pages" : "Pages",
      "Help Center" : "Help Center",
      "Account & Security" : "Account & Security",
      "Invite Friends" : "Invite Friends",
      "Delete Account" : "Delete Account",
      "Logout" : "Logout",
      "Are you sure you want to delete account?" : "Are you sure you want to delete account?",
      "Find Your Spark: Where Connections Ignite." : "Find Your Spark: Where Connections Ignite.",
      "Connecting Hearts, One Swipe at a Time" : "Connecting Hearts, One Swipe at a Time",
      "Discover, Connect, Love: Your Journey Starts Here" : "Discover, Connect, Love: Your Journey Starts Here",
      "It’s match" : "It’s match",
      "Man" : "Man",
      "Woman" : "Woman",
      "Other" : "Other",
      "Both" : "Both",
      "We have sent the OTP to" : "We have sent the OTP to",
      "Confirm Password" : "Confirm Password",
      "Confirm" : "Confirm",
      "Password Not Match" : "Password Not Match",
      "MALE" : "MALE",
      "FEMALE" : "FEMALE",
      "OTHER" : "OTHER",
      "BOTH" : "BOTH",
      "Let's dive in into your account!" : "Let's dive in into your account!",
      "Continue with Email/Mobile Number" : "Continue with Email/Mobile Number",
      "I have an account? " : "I have an account? ",
      "Please Enter Name" : "Please Enter Name",
      "Please Enter Email" : "Please Enter Email",
      "Please Enter Password" : "Please Enter Password",
      "Please Enter MobileNumber" : "Please Enter MobileNumber",
      "Please Enter BirthDate" : "Please Enter BirthDate",
      "Please Select Gender" : "Please Select Gender",
      "Please Select Relationship Goals" : "Please Select Relationship Goals",
      "Please Select Nearby" : "Please Select Nearby",
      "Please Select Hobies" : "Please Select Hobies",
      "Please Select Language" : "Please Select Language",
      "Please Select Religion" : "Please Select Religion",
      "Please Select Minimum 3 Images" : "Please Select Minimum 3 Images",
      "Can you elaborate on your identity? 😎" : "Can you elaborate on your identity? 😎",
      "It will Display on your Profile and you will not able to change it later" : "It will Display on your Profile and you will not able to change it later",
      "Frist Name" : "Frist Name",
      "Your GoMeet identity 😎" : "Your GoMeet identity 😎",
      "Add your phone number and your job to tell others what you do for a living." : "Add your phone number and your job to tell others what you do for a living.",
      "Let's celebrate you 🎂" : "Let's celebrate you 🎂",
      "Tell us your birthdate. Your profile does not display your birthdate, only your age." : "Tell us your birthdate. Your profile does not display your birthdate, only your age.",
      "Choose the gender that best represents you. Authenticity is key to meaningful connections." : "Choose the gender that best represents you. Authenticity is key to meaningful connections.",
      "Be true to yourself 🌟" : "Be true to yourself 🌟",
      "Your relationship goals 💘" : "Your relationship goals 💘",
      "Choose the type of relationship you're seeking on Datify. Love, friendship, or something in between—it's your choice." : "Choose the type of relationship you're seeking on Datify. Love, friendship, or something in between—it's your choice.",
      "Find matches nearby📍" : "Find matches nearby📍",
      "Distance Preference" : "Distance Preference",
      "Select your preferred distance range to discover matches conveniently. We'll help you find love close by." : "Select your preferred distance range to discover matches conveniently. We'll help you find love close by.",
      "Discover like-minded people 🤗" : "Discover like-minded people 🤗",
      "Share your interests, passions, and hobbies. We'll connect you with people who share your enthusiasm." : "Share your interests, passions, and hobbies. We'll connect you with people who share your enthusiasm.",
      "Search." : "Search.",
      "Do you know which languages? 🗺️" : "Do you know which languages? 🗺️",
      "Select your country of origin. We will verify your identity in the next step of your residence." : "Select your country of origin. We will verify your identity in the next step of your residence.",
      "Discover religion 🤗" : "Discover religion 🤗",
      "Search Preference 🌟" : "Search Preference 🌟",
      "Show your best self 📸" : "Show your best self 📸",
      "Upload up to six of your best photos or video to make a fantastic first impression. Let your personality shine." : "Upload up to six of your best photos or video to make a fantastic first impression. Let your personality shine.",
      "Let's Start" : "Let's Start",
      "Skip" : "Skip",
      "Next" : "Next",
      "Recover your account using your email address" : "Recover your account using your email address",
      "<EMAIL>" : "<EMAIL>",
      "We'll email you a link to connect to your account" : "We'll email you a link to connect to your account",
      "Push Notifications" : "Push Notifications",
      "New Matches" : "New Matches",
      "You just got a new match." : "You just got a new match.",
      "Message" : "Message",
      "Someone sent you a new message." : "Someone sent you a new message.",
      "Message Likes" : "Message Likes",
      "Someone liked your message." : "Someone liked your message.",
      "Super Likes" : "Super Likes",
      "You`ve been Super Liked." : "You`ve been Super Liked.",
      "Language" : "Language",
      "Settings" : "Settings",
      "Wallet" : "Wallet",
      "Buy Coin" : "Buy Coin",
      "My Gift" : "My Gift",
      "Refer and earn" : "Refer and earn",
      "Total Balance" : "Total Balance",
      "Add Wallet Amount" : "Add Wallet Amount",
      "Enter Amount" : "Enter Amount",
      "Select Payment Method" : "Select Payment Method",
      "Top-up" : "Top-up",
      "Invite a friend and" : "Invite a friend and",
      "both earn cashback" : "both earn cashback",
      "Invite friend" : "Invite friend",
      "Transaction" : "Transaction",
      "See All" : "See All",
      "Coin can be used for sending gifts only." : "Coin can be used for sending gifts only.",
      "Coins don’t have any expiry date." : "Coins don’t have any expiry date.",
      "Coins can be used with all payment modes." : "Coins can be used with all payment modes.",
      "Coins are credited to your Coin balance only." : "Coins are credited to your Coin balance only.",
      "Coins can be withdrawn with the described method only." : "Coins can be withdrawn with the described method only.",
      "Coins cannot be transferred to any users." : "Coins cannot be transferred to any users.",
      "Coin" : "Coin",
      "History" : "History",
      "Your coin" : "Your coin",
      "Select coin package" : "Select coin package",
      "Please Select Coin Package" : "Please Select Coin Package",
      "Withdraw" : "Withdraw",
      "Coin buying & Info" : "Coin buying & Info",
      "No Gifts Available" : "No Gifts Available",
      "There are no gifts available in your account." : "There are no gifts available in your account.",
      "Refer your friends" : "Refer your friends",
      "& Earn Coins!" : "& Earn Coins!",
      "Invite all your friend to " : "Invite all your friend to ",
      "GoMeet" : "GoMeet",
      " coins when the person you referred purchases a membership." : " coins when the person you referred purchases a membership.",
      "and you receive " : "and you receive ",
      " coins when they sign up using your code" : " coins when they sign up using your code",
      "Your referred person gets " : "Your referred person gets ",
      "Start inviting friends today and enjoy the benefits together!" : "Start inviting friends today and enjoy the benefits together!",
      "Refer a Friend" : "Refer a Friend",
      "Coin History" : "Coin History",
      "Withdraw History" : "Withdraw History",
      "Payout id" : "Payout id",
      "Number of coin" : "Number of coin",
      "Amount" : "Amount",
      "Pay by" : "Pay by",
      "Account Number" : "Account Number",
      "Bank Name" : "Bank Name",
      "Account Name" : "Account Name",
      "Request Date" : "Request Date",
      "Proof" : "Proof",
      "Please select gift" : "Please select gift",
      "Send Gifts" : "Send Gifts",
      "Insufficient coins in wallet" : "Insufficient coins in wallet",
      "Free" : "Free"
    },
  };
}


