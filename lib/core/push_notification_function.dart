// ignore_for_file: avoid_print

import 'package:dating/core/config.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';

Future<void> initPlatformState({context}) async {
  OneSignal.initialize(Config.oneSignel);
  print("OneSignal initialized");

  // Request permission for push notifications
  OneSignal.Notifications.requestPermission(true).then((accepted) {
    print("accepted:------   $accepted");
  });

  // Set up notification permission observer
  OneSignal.Notifications.addPermissionObserver((state) {
    print("Accepted permission state : $state");
  });
}
